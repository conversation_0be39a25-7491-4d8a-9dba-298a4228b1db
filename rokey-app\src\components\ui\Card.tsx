import { HTMLAttributes, forwardRef } from 'react';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient';
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className = '', variant = 'default', hover = false, padding = 'md', children, ...props }, ref) => {
    const baseClasses = 'rounded-xl transition-all duration-200';

    const variants = {
      default: 'card',
      glass: 'glass',
      gradient: 'gradient-surface border border-white/10'
    };

    const paddings = {
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8'
    };

    const hoverClasses = hover ? 'hover:shadow-md hover:-translate-y-1 cursor-pointer' : '';

    return (
      <div
        ref={ref}
        className={`${baseClasses} ${variants[variant]} ${paddings[padding]} ${hoverClasses} ${className}`}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
}

export function CardHeader({ className = '', title, subtitle, action, children, ...props }: CardHeaderProps) {
  return (
    <div className={`flex items-center justify-between mb-6 ${className}`} {...props}>
      <div>
        {title && (
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{title}</h3>
        )}
        {subtitle && (
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{subtitle}</p>
        )}
        {children}
      </div>
      {action && (
        <div>{action}</div>
      )}
    </div>
  );
}

interface CardContentProps extends HTMLAttributes<HTMLDivElement> {}

export function CardContent({ className = '', children, ...props }: CardContentProps) {
  return (
    <div className={`${className}`} {...props}>
      {children}
    </div>
  );
}

interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {}

export function CardFooter({ className = '', children, ...props }: CardFooterProps) {
  return (
    <div className={`mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${className}`} {...props}>
      {children}
    </div>
  );
}

export default Card;
