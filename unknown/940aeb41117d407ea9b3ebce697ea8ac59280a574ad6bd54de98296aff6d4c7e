CREATE TABLE public.config_api_key_complexity_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_api_config_id UUID NOT NULL,
    api_key_id UUID NOT NULL,
    complexity_level INTEGER NOT NULL CHECK (complexity_level >= 1 AND complexity_level <= 5),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,

    CONSTRAINT fk_custom_api_config
        FOREIGN KEY(custom_api_config_id) 
        REFERENCES public.custom_api_configs(id)
        ON DELETE CASCADE,
    
    CONSTRAINT fk_api_key
        FOREIGN KEY(api_key_id)
        REFERENCES public.api_keys(id)
        ON DELETE CASCADE,

    UNIQUE (custom_api_config_id, api_key_id, complexity_level)
);

COMMENT ON TABLE public.config_api_key_complexity_assignments IS 'Stores assignments of API keys to specific complexity levels for a given API Model Configuration.';
COMMENT ON COLUMN public.config_api_key_complexity_assignments.complexity_level IS 'Complexity level (e.g., 1-5) assigned to the API key for this configuration.';

-- Trigger to update "updated_at" timestamp on row update
CREATE TRIGGER handle_updated_at_config_api_key_complexity_assignments
BEFORE UPDATE ON public.config_api_key_complexity_assignments
FOR EACH ROW
EXECUTE FUNCTION moddatetime (updated_at);

ALTER TABLE public.request_logs
ADD COLUMN classified_complexity_level INTEGER,
ADD COLUMN classified_complexity_llm TEXT;

COMMENT ON COLUMN public.request_logs.classified_complexity_level IS 'Complexity level classified by the backend LLM for Complexity-Based Round Robin routing.';
COMMENT ON COLUMN public.request_logs.classified_complexity_llm IS 'The backend LLM model used for complexity classification.'; 