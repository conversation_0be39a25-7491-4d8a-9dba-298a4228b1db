-- Comprehensive RoKey Database Schema - Corrected for "capabilities" and provider check

-- Enable pgcrypto extension if not already enabled (for gen_random_uuid())
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA public;

-- Trigger function to update 'updated_at' column
CREATE OR REPLACE FUNCTION public.moddatetime()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop tables in reverse order of dependency or use CASCADE
DROP TABLE IF EXISTS public.request_logs CASCADE;
DROP TABLE IF EXISTS public.api_key_role_assignments CASCADE;
DROP TABLE IF EXISTS public.api_keys CASCADE;
DROP TABLE IF EXISTS public.custom_api_configs CASCADE;
DROP TABLE IF EXISTS public.models CASCADE;

-- Table: models
CREATE TABLE public.models (
    id TEXT PRIMARY KEY,                        -- Corresponds to OpenRouter model ID
    name TEXT NOT NULL,                       -- Original model name from OpenRouter (e.g., "gpt-3.5-turbo")
    display_name TEXT,                        -- User-friendly name (e.g., "GPT-3.5 Turbo")
    provider_id TEXT NOT NULL,                -- Your internal provider slug (e.g., "openai", "google")
    version TEXT,                             -- Model version, if available (e.g., "2024-02-15")
    family TEXT,                              -- Model family (e.g., "GPT-3.5", "Claude 2")
    description TEXT,
    context_window INTEGER,
    input_token_limit INTEGER,                -- Max input tokens
    output_token_limit INTEGER,               -- Max output tokens
    modality TEXT,                            -- e.g., 'text', 'image', 'multimodal'
    is_public BOOLEAN DEFAULT TRUE,           -- If the model is generally available via OpenRouter
    provider_specific_details JSONB,          -- Raw provider-specific details from OpenRouter (architecture, pricing etc.)
    model_metadata JSONB,                     -- General metadata, e.g., source of this record
    api_url TEXT,                             -- Base API URL (can be specific if not using OpenRouter proxy)
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.models IS 'Stores LLM models, primarily sourced from OpenRouter and enriched.';
COMMENT ON COLUMN public.models.id IS 'Unique ID for the model, typically from OpenRouter (e.g., openai/gpt-3.5-turbo).';
COMMENT ON COLUMN public.models.name IS 'The original model name as provided by the source (e.g., gpt-3.5-turbo).';
COMMENT ON COLUMN public.models.display_name IS 'A more user-friendly or curated name for display purposes.';
COMMENT ON COLUMN public.models.provider_id IS 'Internal RoKey provider identifier (e.g., openai, google, anthropic).';
COMMENT ON COLUMN public.models.version IS 'Version identifier for the model, if applicable.';
COMMENT ON COLUMN public.models.family IS 'The model family or series (e.g., GPT-4, Claude 3, Llama 2).';
COMMENT ON COLUMN public.models.context_window IS 'The maximum context window size in tokens.';
COMMENT ON COLUMN public.models.input_token_limit IS 'The maximum number of input tokens allowed.';
COMMENT ON COLUMN public.models.output_token_limit IS 'The maximum number of output tokens that can be generated.';
COMMENT ON COLUMN public.models.modality IS 'Primary modality of the model (text, image, audio, multimodal, etc.).';
COMMENT ON COLUMN public.models.is_public IS 'Indicates if the model is generally available or more restricted.';
COMMENT ON COLUMN public.models.provider_specific_details IS 'Raw JSON blob from OpenRouter containing architecture, pricing, top_provider_info, etc.';
COMMENT ON COLUMN public.models.model_metadata IS 'General metadata about this model entry, e.g., {"source": "openrouter_v1_models_list"}.';

-- Table: custom_api_configs
CREATE TABLE public.custom_api_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID, -- FK to auth.users.id - will be enforced later
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
    -- CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE -- Add when auth is integrated
);
CREATE INDEX idx_custom_api_configs_user_id ON public.custom_api_configs(user_id);
COMMENT ON TABLE public.custom_api_configs IS 'User-defined configurations/groupings for API keys.';

-- Table: api_keys
-- Provider list for the check constraint
-- Derived from unique model.provider values in config/models.ts
-- 'OpenAI', 'Google', 'Anthropic', 'Meta AI', 'Mistral AI', 'DeepSeek', 
-- 'Alibaba (Qwen)', 'Cohere', 'xAI (Grok)', 'Technology Innovation Institute (Falcon)', 
-- 'Perplexity', 'NVIDIA NIM Models', 'Baichuan Inc.', 
-- 'Microsoft', 'Agentica', 'Tsinghua University', (HuggingFace section)
-- 'Azure OpenAI Models', 'Yi Models', 'Ollama (Local Deployment Models)'
CREATE TABLE public.api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_api_config_id UUID NOT NULL,
    user_id UUID, -- Denormalized for easier policy, or derive from custom_api_config_id. Will be FK to auth.users.id
    provider TEXT NOT NULL,
    predefined_model_id TEXT NOT NULL, -- FK to models.id
    label TEXT NOT NULL,
    encrypted_api_key TEXT NOT NULL, -- Stores combined iv:authTag:encryptedData
    api_key_hash TEXT NOT NULL, -- Removed global UNIQUE constraint here
    status TEXT DEFAULT 'active' NOT NULL CHECK (status IN ('active', 'inactive', 'revoked')),
    is_default_general_chat_model BOOLEAN DEFAULT false NOT NULL,
    last_used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT fk_custom_api_config FOREIGN KEY (custom_api_config_id) REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
    CONSTRAINT fk_predefined_model FOREIGN KEY (predefined_model_id) REFERENCES public.models(id) ON DELETE RESTRICT,
    -- CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE, -- Add when auth is integrated
    CONSTRAINT unique_key_hash_per_config UNIQUE (custom_api_config_id, api_key_hash), -- Added composite unique constraint
    CONSTRAINT api_keys_provider_check CHECK (provider IN (
        'OpenAI', 'Google', 'Anthropic', 'Meta AI', 'Mistral AI', 'DeepSeek',
        'Alibaba (Qwen)', 'Cohere', 'xAI (Grok)', 'Technology Innovation Institute (Falcon)',
        'Perplexity', 'NVIDIA NIM Models', 'Baichuan Inc.',
        'Microsoft', 'Agentica', 'Tsinghua University',
        'Azure OpenAI Models', 'Yi Models', 'Ollama (Local Deployment Models)',
        'Custom LLM Server', 'OpenRouter' -- Added OpenRouter
    ))
);
CREATE INDEX idx_api_keys_custom_api_config_id ON public.api_keys(custom_api_config_id);
CREATE INDEX idx_api_keys_user_id ON public.api_keys(user_id);
CREATE INDEX idx_api_keys_predefined_model_id ON public.api_keys(predefined_model_id);

-- Partial unique index to ensure only one default general chat model per custom_api_config
CREATE UNIQUE INDEX unique_default_general_chat_model_per_config
ON public.api_keys (custom_api_config_id)
WHERE is_default_general_chat_model = TRUE;

COMMENT ON TABLE public.api_keys IS 'Stores encrypted API keys, linked to configurations and predefined models.';
COMMENT ON COLUMN public.api_keys.encrypted_api_key IS 'AES-256-GCM encrypted key, format: iv:authTag:encryptedData';
COMMENT ON COLUMN public.api_keys.api_key_hash IS 'SHA-256 hash of the raw API key for uniqueness checks.';
COMMENT ON COLUMN public.api_keys.predefined_model_id IS 'References a specific model variant from the models table.';

-- Table: api_key_role_assignments
CREATE TABLE public.api_key_role_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID NOT NULL,
    custom_api_config_id UUID NOT NULL, -- Denormalized for easier constraint and querying
    role_name TEXT NOT NULL, -- e.g., 'Logic', 'Copywriting', 'Coding'. From config/roles.ts
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT fk_api_key FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_api_config FOREIGN KEY (custom_api_config_id) REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
    CONSTRAINT unique_api_key_role UNIQUE (api_key_id, role_name),
    CONSTRAINT unique_role_per_custom_config UNIQUE (custom_api_config_id, role_name)
);
CREATE INDEX idx_api_key_role_assignments_api_key_id ON public.api_key_role_assignments(api_key_id);
CREATE INDEX idx_api_key_role_assignments_custom_config_id ON public.api_key_role_assignments(custom_api_config_id);

COMMENT ON TABLE public.api_key_role_assignments IS 'Assigns predefined roles to API keys within a custom configuration.';
COMMENT ON COLUMN public.api_key_role_assignments.role_name IS 'Name of the role, e.g., "Coding", "Logic". Must match a predefined role.';
COMMENT ON CONSTRAINT unique_role_per_custom_config ON public.api_key_role_assignments IS 'Ensures a role is assigned to only one API key within a given Custom API Configuration.';

-- Table: request_logs
CREATE TABLE public.request_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_api_config_id UUID, -- FK to custom_api_configs.id
    api_key_id UUID, -- FK to api_keys.id (the actual key used)
    user_id UUID, -- FK to auth.users.id
    predefined_model_id TEXT, -- FK to models.id (model used)
    role_requested TEXT, -- The role specified in the API request (if any)
    role_used TEXT, -- The role that ultimately handled the request (could be default)
    ip_address INET,
    request_timestamp TIMESTAMPTZ DEFAULT now() NOT NULL,
    response_timestamp TIMESTAMPTZ,
    status_code INT,
    request_payload JSONB,
    response_payload JSONB,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT fk_custom_api_config FOREIGN KEY (custom_api_config_id) REFERENCES public.custom_api_configs(id) ON DELETE SET NULL,
    CONSTRAINT fk_api_key FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id) ON DELETE SET NULL,
    CONSTRAINT fk_predefined_model FOREIGN KEY (predefined_model_id) REFERENCES public.models(id) ON DELETE SET NULL
    -- CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL -- Add when auth is integrated
);
CREATE INDEX idx_request_logs_custom_api_config_id ON public.request_logs(custom_api_config_id);
CREATE INDEX idx_request_logs_api_key_id ON public.request_logs(api_key_id);
CREATE INDEX idx_request_logs_user_id ON public.request_logs(user_id);
CREATE INDEX idx_request_logs_request_timestamp ON public.request_logs(request_timestamp);

COMMENT ON TABLE public.request_logs IS 'Logs incoming requests to the unified API endpoint and their outcomes.';

-- Apply triggers for updated_at
DROP TRIGGER IF EXISTS handle_updated_at_models ON public.models;
CREATE TRIGGER handle_updated_at_models BEFORE UPDATE ON public.models FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

DROP TRIGGER IF EXISTS handle_updated_at_custom_api_configs ON public.custom_api_configs;
CREATE TRIGGER handle_updated_at_custom_api_configs BEFORE UPDATE ON public.custom_api_configs FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

DROP TRIGGER IF EXISTS handle_updated_at_api_keys ON public.api_keys;
CREATE TRIGGER handle_updated_at_api_keys BEFORE UPDATE ON public.api_keys FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

DROP TRIGGER IF EXISTS handle_updated_at_api_key_role_assignments ON public.api_key_role_assignments;
CREATE TRIGGER handle_updated_at_api_key_role_assignments BEFORE UPDATE ON public.api_key_role_assignments FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

DROP TRIGGER IF EXISTS handle_updated_at_request_logs ON public.request_logs;
CREATE TRIGGER handle_updated_at_request_logs BEFORE UPDATE ON public.request_logs FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- Populate models table (No 'capabilities' column)
-- INSERT statements for public.models have been removed.
-- The public.models table will now be populated by the supabaseWriter.py script.

SELECT 'Comprehensive schema script created successfully. Please review and execute.'; 