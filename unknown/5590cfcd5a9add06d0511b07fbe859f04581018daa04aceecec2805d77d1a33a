CREATE OR R<PERSON>LACE FUNCTION public.update_api_key_complexity_assignments(
    p_config_id UUID,
    p_api_key_id UUID,
    p_complexity_levels INTEGER[]
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    level INTEGER;
BEGIN
    -- Start a transaction block if not already in one (plpgsql functions run in a transaction by default)

    -- Delete existing assignments for this config and API key
    DELETE FROM public.config_api_key_complexity_assignments
    WHERE custom_api_config_id = p_config_id
      AND api_key_id = p_api_key_id;

    -- Insert new assignments if p_complexity_levels is not null and not empty
    IF p_complexity_levels IS NOT NULL AND array_length(p_complexity_levels, 1) > 0 THEN
        FOREACH level IN ARRAY p_complexity_levels
        LOOP
            INSERT INTO public.config_api_key_complexity_assignments (
                custom_api_config_id, 
                api_key_id, 
                complexity_level
            ) VALUES (
                p_config_id, 
                p_api_key_id, 
                level
            );
        END LOOP;
    END IF;

    -- Transaction will be committed automatically upon successful completion of the function
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error (optional, depends on your logging setup)
        RAISE INFO 'Error in update_api_key_complexity_assignments: %', SQLERRM;
        -- Re-throw the error to ensure the transaction is rolled back
        RAISE;
END;
$$;

COMMENT ON FUNCTION public.update_api_key_complexity_assignments(UUID, UUID, INTEGER[]) 
IS 'Updates the complexity level assignments for a given API key within a custom API configuration. Deletes existing assignments and inserts new ones based on the provided array of levels.';

-- Example of how to grant execute permission if needed (adjust role as necessary)
-- GRANT EXECUTE ON FUNCTION public.update_api_key_complexity_assignments(UUID, UUID, INTEGER[]) TO authenticated;
-- GRANT EXECUTE ON FUNCTION public.update_api_key_complexity_assignments(UUID, UUID, INTEGER[]) TO service_role; 