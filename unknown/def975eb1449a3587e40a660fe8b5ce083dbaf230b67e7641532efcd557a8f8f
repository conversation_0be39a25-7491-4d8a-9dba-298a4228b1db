# RoKey - Smart LLM API Infrastructure Tool

RoKey is a Next.js application designed to provide a robust infrastructure for managing and routing Large Language Model (LLM) API requests. It allows users to securely store their API keys for various LLM providers, define custom "API Configurations" (which are named groups of these keys), and set up intelligent routing rules to distribute requests across different models and providers based on various strategies (e.g., load balancing, failover, cost optimization, best quality). The system also aims to offer comprehensive logging and analytics for API usage.

## Core Features:

1.  **Secure API Key Management:**
    *   Encrypted storage of API keys for multiple LLM providers.
    *   Users can create named "Custom API Configurations" (or "User Models").
    *   Within each configuration, users can add, view, and delete multiple provider API keys.
    *   Each added API key is associated with a specific predefined LLM variant (e.g., GPT-4o, Gemini 2.5 Pro).
2.  **Dynamic Model Variant Handling:**
    *   Initially, a predefined list of common LLMs and their providers will be available.
    *   Future enhancement: Dynamic fetching/updating of available models from providers.
3.  **Intelligent Routing Rules:**
    *   Users can define rules to route incoming API requests.
    *   Rules can be based on conditions (e.g., prompt characteristics, user group).
    *   Supported strategies: Load Balancing, Failover, Cost Optimization, Best Quality.
    *   Routing will direct requests to appropriate API keys within a selected Custom API Configuration.
4.  **Request Logging & Analytics:**
    *   Detailed logging of all API requests and responses.
    *   Analytics dashboard to visualize usage, costs, performance, and error rates.
5.  **Unified API Endpoint:**
    *   RoKey will expose its own API endpoint. Users will send requests to this endpoint, and RoKey will handle the routing and interaction with the underlying LLM providers.
6.  **User Authentication:**
    *   Secure user accounts to manage their keys, configurations, and rules.

## Technical Stack:

*   **Framework:** Next.js (with App Router)
*   **Language:** TypeScript
*   **Styling:** Tailwind CSS
*   **Database:** Supabase (PostgreSQL)
*   **Encryption:** AES-256-GCM for API keys at rest.

🌐 RoKey – Full Project Brief
🔧 What is RoKey?
RoKey is a smart LLM API infrastructure tool for developers, startups, and agencies that enables:

Advanced API key routing across different LLM models and model variants

Unified access to all LLMs via one API key

Custom role assignment per model (e.g., GPT-4o for logic, Claude for writing)

Automatic fallback across API keys on error or rate limits

User-driven or auto role-assignment based on model strength

Finetuning and knowledge injection into routed models via document/file uploads

Smart routing engine that evolves with the user's data and task preferences

🧠 How the App Works – From the User's Perspective
Connect API Keys:

Upload any number of API keys (e.g., 3 Gemini keys, 2 GPT keys)

Select model variant for each key (e.g., Gemini 2.5 Pro, GPT-4o, Claude 3.5)

Assign Roles:

Use dropdowns to assign roles like:

Copywriting

Coding

Logic/Reasoning

Summarization

Research

Assign models manually OR auto-assign based on default strengths

Choose Routing Behavior:

Role routing: route tasks to specific models based on task

Fallback mode: rotate keys only when limits are hit

Hybrid: role routing first, fallback kicks in if model fails

Train Your Setup:

Upload documents/code to train your routed model combo

Top-tier users can finetune individual models first, then train the combo

Each model becomes stronger at its assigned task

Send Requests:

Use your RoKey unified API key to send prompts

RoKey intelligently routes based on role, availability, and your training data

Monitor:

Track usage per key/model

View cost, token counts, success/fail logs

Manage subscription & credits

🧩 Final App Feature Stack
Module	Function
🔐 Key Management	Add multiple API keys, select model variant for each
🎛️ Role Assignment	Assign roles (e.g. Coding) to specific models
🔁 Routing Engine	Smart routing (role + fallback + hybrid)
🔧 Finetuning	Upload files to train models individually or in combination
🧠 Smart Auto-Router	Optional model selects the best route based on content
🧾 Usage Logs	See model used, tokens, time, errors
💳 Credit System	Proprietary router and finetuning cost credits
👤 Auth & Plans	User access, limits, and payment (added at final stage) 