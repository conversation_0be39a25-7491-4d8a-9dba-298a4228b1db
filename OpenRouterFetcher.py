import requests
import json
import os

# --- CONFIGURATION ---
OPENROUTER_API_KEY = "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" 
OUTPUT_FILE = "openrouter_models_raw.json"
# Recommended headers by OpenRouter
YOUR_SITE_URL = "https://rokey.app" # Or your actual site URL
YOUR_SITE_NAME = "RoKey" # Or your actual site name
# --- END CONFIGURATION ---

def fetch_openrouter_models():
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": YOUR_SITE_URL,
        "X-Title": YOUR_SITE_NAME,
        "Content-Type": "application/json"
    }
    url = "https://openrouter.ai/api/v1/models"

    print(f"Fetching models from {url}...")
    try:
        response = requests.get(url, headers=headers, timeout=120) # 120 seconds timeout
        response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)
        
        models_data = response.json()
        
        # Save to file
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(models_data, f, ensure_ascii=False, indent=4)
        
        print(f"Successfully fetched models.")
        if 'data' in models_data and isinstance(models_data['data'], list):
            print(f"Number of models received: {len(models_data['data'])}")
        else:
            print("Warning: The response structure might be different than expected (no 'data' array).")
        print(f"Raw model data saved to: {os.path.abspath(OUTPUT_FILE)}")

    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
        print(f"Response content: {response.content.decode('utf-8', errors='ignore')}")
    except requests.exceptions.RequestException as req_err:
        print(f"Request error occurred: {req_err}")
    except json.JSONDecodeError:
        print("Failed to decode JSON from response.")
        print(f"Response content: {response.text}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    fetch_openrouter_models()