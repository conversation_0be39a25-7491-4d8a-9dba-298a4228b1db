-- R<PERSON><PERSON><PERSON> App: Data Loss Investigation Script
-- Run this in your Supabase SQL Editor to investigate the data loss

-- =====================================================
-- STEP 1: INVESTIGATE CURRENT STATE
-- =====================================================

-- Check all training jobs for your configuration
SELECT 
    id,
    name,
    description,
    status,
    created_at,
    updated_at,
    (training_data->>'file_count')::int as file_count_in_data
FROM training_jobs 
WHERE custom_api_config_id = 'YOUR_CONFIG_ID_HERE'  -- Replace with your actual config ID
ORDER BY created_at DESC;

-- Check training files for each job
SELECT 
    tf.id,
    tf.training_job_id,
    tf.original_filename,
    tf.file_size,
    tf.processing_status,
    tf.created_at,
    tj.name as job_name,
    tj.created_at as job_created_at
FROM training_files tf
JOIN training_jobs tj ON tf.training_job_id = tj.id
WHERE tj.custom_api_config_id = 'YOUR_CONFIG_ID_HERE'  -- Replace with your actual config ID
ORDER BY tj.created_at DESC, tf.created_at DESC;

-- Count files per training job
SELECT 
    tj.id as job_id,
    tj.name as job_name,
    tj.created_at as job_created,
    COUNT(tf.id) as actual_file_count,
    (tj.training_data->>'file_count')::int as reported_file_count
FROM training_jobs tj
LEFT JOIN training_files tf ON tj.id = tf.training_job_id
WHERE tj.custom_api_config_id = 'YOUR_CONFIG_ID_HERE'  -- Replace with your actual config ID
GROUP BY tj.id, tj.name, tj.created_at, tj.training_data
ORDER BY tj.created_at DESC;

-- =====================================================
-- STEP 2: CHECK FOR ORPHANED FILES
-- =====================================================

-- Look for any training files that might be orphaned
SELECT 
    tf.id,
    tf.original_filename,
    tf.training_job_id,
    tf.created_at,
    'ORPHANED - Job deleted' as status
FROM training_files tf
LEFT JOIN training_jobs tj ON tf.training_job_id = tj.id
WHERE tj.id IS NULL;

-- =====================================================
-- INSTRUCTIONS:
-- =====================================================
-- 1. Replace 'YOUR_CONFIG_ID_HERE' with your actual custom_api_config_id
-- 2. Run these queries to see the current state
-- 3. The fix in training/page.tsx will prevent this from happening again
-- 4. You'll need to re-upload the lost files
