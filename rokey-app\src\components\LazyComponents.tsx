// Phase 2B: Advanced Component Lazy Loading System
'use client';

import React, { lazy, Suspense, ComponentType, ReactNode } from 'react';

// Enhanced loading skeleton with better UX
const EnhancedSkeleton = ({ 
  height = '200px', 
  className = '',
  variant = 'default' 
}: { 
  height?: string; 
  className?: string;
  variant?: 'default' | 'card' | 'list' | 'chart';
}) => {
  const getSkeletonContent = () => {
    switch (variant) {
      case 'card':
        return (
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
            <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
          </div>
        );
      case 'list':
        return (
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        );
      case 'chart':
        return (
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse"></div>
            <div className="h-40 bg-gray-200 rounded animate-pulse"></div>
            <div className="flex justify-between">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
              ))}
            </div>
          </div>
        );
      default:
        return <div className={`bg-gray-200 rounded animate-pulse`} style={{ height }}></div>;
    }
  };

  return (
    <div className={`p-4 ${className}`}>
      {getSkeletonContent()}
    </div>
  );
};

// Higher-order component for lazy loading with enhanced features
export function withLazyLoading<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: {
    fallback?: ReactNode;
    errorBoundary?: boolean;
    preload?: boolean;
    variant?: 'default' | 'card' | 'list' | 'chart';
    height?: string;
  } = {}
) {
  const LazyComponent = lazy(importFn);
  
  // Preload component if requested
  if (options.preload) {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      importFn().catch(console.warn);
    }, 100);
  }

  return function LazyWrapper(props: P) {
    const fallback = options.fallback || (
      <EnhancedSkeleton 
        variant={options.variant} 
        height={options.height}
      />
    );

    if (options.errorBoundary) {
      return (
        <ErrorBoundary fallback={fallback}>
          <Suspense fallback={fallback}>
            <LazyComponent {...props} />
          </Suspense>
        </ErrorBoundary>
      );
    }

    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

// Simple error boundary for lazy components
class ErrorBoundary extends React.Component<
  { children: ReactNode; fallback: ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: ReactNode; fallback: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.warn('Lazy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

// Lazy-loaded components for common heavy components
export const LazyMarkdownRenderer = withLazyLoading(
  () => import('./MarkdownRenderer'),
  {
    variant: 'default',
    height: '100px',
    preload: true // Preload since it's commonly used
  }
);

// Example usage for future components:
// export const LazyDataTable = withLazyLoading(
//   () => import('./DataTable'),
//   {
//     variant: 'list',
//     errorBoundary: true
//   }
// );

// export const LazyChart = withLazyLoading(
//   () => import('./Chart'),
//   {
//     variant: 'chart',
//     errorBoundary: true
//   }
// );

// Phase 2B: Enhanced lazy loading for existing components
export const LazyLoadingSkeleton = withLazyLoading(
  () => import('./LoadingSkeleton').then(mod => ({ default: mod.ChatHistorySkeleton })),
  {
    variant: 'list',
    errorBoundary: true
  }
);

// Intersection Observer based lazy loading for viewport-based loading
export function LazyOnVisible({ 
  children, 
  fallback, 
  rootMargin = '50px',
  threshold = 0.1 
}: {
  children: ReactNode;
  fallback?: ReactNode;
  rootMargin?: string;
  threshold?: number;
}) {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { rootMargin, threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold]);

  return (
    <div ref={ref}>
      {isVisible ? children : (fallback || <EnhancedSkeleton />)}
    </div>
  );
}

// Hook for progressive loading of heavy components
export function useProgressiveLoading(components: string[], delay = 100) {
  const [loadedComponents, setLoadedComponents] = React.useState<Set<string>>(new Set());

  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let currentIndex = 0;

    const loadNext = () => {
      if (currentIndex < components.length) {
        setLoadedComponents(prev => new Set([...prev, components[currentIndex]]));
        currentIndex++;
        timeoutId = setTimeout(loadNext, delay);
      }
    };

    // Start loading after initial render
    timeoutId = setTimeout(loadNext, delay);

    return () => clearTimeout(timeoutId);
  }, [components, delay]);

  return {
    isLoaded: (component: string) => loadedComponents.has(component),
    loadedCount: loadedComponents.size,
    totalCount: components.length,
    progress: (loadedComponents.size / components.length) * 100
  };
}

// Performance monitoring for lazy components
export function useLazyLoadingMetrics() {
  const metrics = React.useRef({
    componentsLoaded: 0,
    totalLoadTime: 0,
    averageLoadTime: 0,
    errors: 0
  });

  const trackComponentLoad = React.useCallback((componentName: string, loadTime: number) => {
    metrics.current.componentsLoaded++;
    metrics.current.totalLoadTime += loadTime;
    metrics.current.averageLoadTime = metrics.current.totalLoadTime / metrics.current.componentsLoaded;
    
    console.log(`[Lazy Loading] ${componentName} loaded in ${loadTime}ms`);
  }, []);

  const trackError = React.useCallback((componentName: string, error: Error) => {
    metrics.current.errors++;
    console.warn(`[Lazy Loading] ${componentName} failed to load:`, error);
  }, []);

  const getMetrics = React.useCallback(() => ({ ...metrics.current }), []);

  return {
    trackComponentLoad,
    trackError,
    getMetrics
  };
}

// Export utility functions
export { EnhancedSkeleton };
