// Shared training data cache for cross-endpoint access
// This allows cache invalidation from upsert operations

interface TrainingCacheEntry {
  data: any;
  timestamp: number;
  jobId: string;
}

class TrainingDataCache {
  private cache = new Map<string, TrainingCacheEntry>();
  private readonly TTL = 300000; // 5 minutes

  set(configId: string, data: any, jobId: string): void {
    this.cache.set(configId, {
      data,
      timestamp: Date.now(),
      jobId
    });
  }

  get(configId: string): TrainingCacheEntry | null {
    const entry = this.cache.get(configId);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(configId);
      return null;
    }

    return entry;
  }

  invalidate(configId: string): boolean {
    return this.cache.delete(configId);
  }

  clear(): void {
    this.cache.clear();
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.TTL) {
        this.cache.delete(key);
      }
    }
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Global instance
export const trainingDataCache = new TrainingDataCache();

// Cleanup interval
setInterval(() => {
  trainingDataCache.cleanup();
}, 600000); // Every 10 minutes

// Cache invalidation API endpoint helper
export async function invalidateTrainingCache(configId: string): Promise<boolean> {
  try {
    // Call the cache invalidation endpoint
    const response = await fetch('/api/cache/invalidate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'training', configId })
    });
    
    return response.ok;
  } catch (error) {
    console.warn('Failed to invalidate training cache:', error);
    return false;
  }
}
