// Streaming utilities for first token tracking and performance monitoring

export function createFirstTokenTrackingStream(
  originalStream: ReadableStream,
  provider: string,
  model: string
): ReadableStream {
  const reader = originalStream.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();
  
  return new ReadableStream({
    async start(controller) {
      let firstTokenSent = false;
      const streamStartTime = Date.now();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            controller.close();
            break;
          }
          
          const chunk = decoder.decode(value, { stream: true });
          
          // Check if this chunk contains actual content (first token)
          if (!firstTokenSent && chunk.includes('delta')) {
            try {
              // Parse SSE data to check for content
              const lines = chunk.split('\n');
              for (const line of lines) {
                if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                  const jsonData = line.substring(6);
                  try {
                    const parsed = JSON.parse(jsonData);
                    if (parsed.choices?.[0]?.delta?.content) {
                      const firstTokenTime = Date.now() - streamStartTime;
                      console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model})`);
                      firstTokenSent = true;
                      break;
                    }
                  } catch (e) {
                    // Ignore JSON parse errors for individual chunks
                  }
                }
              }
            } catch (e) {
              // Ignore parsing errors, just track timing
              if (!firstTokenSent) {
                const firstTokenTime = Date.now() - streamStartTime;
                console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model}) [fallback detection]`);
                firstTokenSent = true;
              }
            }
          }
          
          // Forward the chunk unchanged
          controller.enqueue(value);
        }
      } catch (error) {
        console.error(`[${provider} Stream Tracking] Error:`, error);
        controller.error(error);
      }
    }
  });
}

// Enhanced logging for streaming performance
export function logStreamingPerformance(
  provider: string,
  model: string,
  metrics: {
    timeToFirstToken?: number;
    totalStreamTime?: number;
    totalTokens?: number;
    averageTokenLatency?: number;
  }
) {
  console.log(`📊 STREAMING PERFORMANCE: ${provider}/${model}`);
  
  if (metrics.timeToFirstToken !== undefined) {
    console.log(`   ⏱️ Time to First Token: ${metrics.timeToFirstToken.toFixed(1)}ms`);
    
    // Performance categories
    if (metrics.timeToFirstToken < 500) {
      console.log(`   ⚡ EXCELLENT first token performance`);
    } else if (metrics.timeToFirstToken < 1000) {
      console.log(`   ✅ GOOD first token performance`);
    } else if (metrics.timeToFirstToken < 2000) {
      console.log(`   ⚠️ SLOW first token performance`);
    } else {
      console.log(`   🐌 VERY SLOW first token performance`);
    }
  }
  
  if (metrics.totalStreamTime !== undefined) {
    console.log(`   🔄 Total Stream Time: ${metrics.totalStreamTime.toFixed(1)}ms`);
  }
  
  if (metrics.totalTokens !== undefined) {
    console.log(`   🎯 Total Tokens: ${metrics.totalTokens}`);
  }
  
  if (metrics.averageTokenLatency !== undefined) {
    console.log(`   📈 Avg Token Latency: ${metrics.averageTokenLatency.toFixed(1)}ms/token`);
  }
}

// Utility to extract provider and model from request context
export function getProviderModelFromContext(
  providerName: string | null,
  modelId: string | null
): { provider: string; model: string } {
  return {
    provider: providerName || 'unknown',
    model: modelId || 'unknown'
  };
}

// Simple token counter for rough estimation
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  // This is a simplified approach, real tokenization would be more accurate
  return Math.ceil(text.length / 4);
}

// Performance thresholds for different providers
export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT_FIRST_TOKEN: 500,   // < 500ms
  GOOD_FIRST_TOKEN: 1000,       // < 1000ms
  SLOW_FIRST_TOKEN: 2000,       // < 2000ms
  // Anything above 2000ms is considered very slow
  
  EXCELLENT_TOTAL: 3000,        // < 3s total
  GOOD_TOTAL: 5000,            // < 5s total
  SLOW_TOTAL: 10000,           // < 10s total
  
  TARGET_TOKEN_LATENCY: 50,     // < 50ms per token after first
} as const;

// Check if performance meets targets
export function evaluatePerformance(metrics: {
  timeToFirstToken?: number;
  totalStreamTime?: number;
  averageTokenLatency?: number;
}): {
  firstTokenGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
  totalTimeGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
  tokenLatencyGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
  overallGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
} {
  const firstTokenGrade = 
    !metrics.timeToFirstToken ? 'very_slow' :
    metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' :
    metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' :
    metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';
  
  const totalTimeGrade = 
    !metrics.totalStreamTime ? 'very_slow' :
    metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' :
    metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' :
    metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';
  
  const tokenLatencyGrade = 
    !metrics.averageTokenLatency ? 'very_slow' :
    metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' :
    metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' :
    metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';
  
  // Overall grade is the worst of the three
  const grades = [firstTokenGrade, totalTimeGrade, tokenLatencyGrade];
  const gradeOrder = ['excellent', 'good', 'slow', 'very_slow'];
  const overallGrade = grades.reduce((worst, current) => {
    return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;
  }, 'excellent') as 'excellent' | 'good' | 'slow' | 'very_slow';
  
  return {
    firstTokenGrade,
    totalTimeGrade,
    tokenLatencyGrade,
    overallGrade
  };
}
