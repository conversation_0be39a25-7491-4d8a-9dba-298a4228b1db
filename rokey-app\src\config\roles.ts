export interface Role {
  id: string; // e.g., 'logic', 'copywriting'
  name: string; // e.g., 'Logic & Reasoning', 'Copywriting & Content Creation'
  description?: string; // Optional: A brief explanation of the role
}

export const PREDEFINED_ROLES: Role[] = [
  {
    id: 'general_chat',
    name: 'General Chat',
    description: 'Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback.'
  },
  {
    id: 'logic_reasoning',
    name: 'Logic & Reasoning',
    description: 'For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking.'
  },
  {
    id: 'writing',
    name: 'Writing & Content Creation',
    description: 'For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more.'
  },
  {
    id: 'coding_frontend',
    name: 'Coding - Frontend',
    description: 'For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.).'
  },
  {
    id: 'coding_backend',
    name: 'Coding - Backend',
    description: 'For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.).'
  },
  {
    id: 'research_synthesis',
    name: 'Research & Synthesis',
    description: 'For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries.'
  },
  {
    id: 'summarization_briefing',
    name: 'Summarization & Briefing',
    description: 'For condensing long texts, documents, or conversations into concise summaries or executive briefings.'
  },
  {
    id: 'translation_localization',
    name: 'Translation & Localization',
    description: 'For translating text between languages and adapting content culturally for different locales.'
  },
  {
    id: 'data_extraction_structuring',
    name: 'Data Extraction & Structuring',
    description: 'For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it.'
  },
  {
    id: 'brainstorming_ideation',
    name: 'Brainstorming & Ideation',
    description: 'For generating new ideas, exploring concepts, and creative problem-solving sessions.'
  },
  {
    id: 'education_tutoring',
    name: 'Education & Tutoring',
    description: 'For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects.'
  },
  {
    id: 'image_generation',
    name: 'Image Generation',
    description: 'For creating images from textual descriptions. Assign to keys linked to image generation models.'
  },
  {
    id: 'audio_transcription',
    name: 'Audio Transcription',
    description: 'For converting speech from audio files into written text. Assign to keys linked to transcription models.'
  }
  // TODO: Consider adding more specialized roles for legal, financial, or specific industry tasks if needed.
];

export const getRoleById = (id: string): Role | undefined => {
  return PREDEFINED_ROLES.find(role => role.id === id);
};

export const getRoleName = (id: string): string | undefined => {
  return PREDEFINED_ROLES.find(role => role.id === id)?.name;
}; 