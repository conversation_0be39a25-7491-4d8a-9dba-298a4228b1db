/**
 * Utility functions for transforming debug-style log messages into production-ready, user-friendly text
 */

export interface RoleUsedInfo {
  text: string;
  type: 'success' | 'error' | 'fallback' | 'role';
  details?: string;
}

/**
 * Check if a string looks like a role name (vs a technical debug pattern)
 */
const isLikelyRoleName = (str: string): boolean => {
  // Exclude obvious technical patterns
  const technicalPatterns = [
    /default_key/i,
    /attempt_\d+/i,
    /status_\d+/i,
    /failed/i,
    /success/i,
    /complexity_rr/i,
    /fallback_position/i,
    /^[a-f0-9-]{8,}/i, // UUIDs or long hex strings
    /_then_/i,
    /classification_/i,
    /no_prompt/i,
    /error/i
  ];
  
  // If it matches any technical pattern, it's not a role name
  if (technicalPatterns.some(pattern => pattern.test(str))) {
    return false;
  }
  
  // If it's a simple word or snake_case without numbers/technical terms, likely a role
  return /^[a-z_]+$/i.test(str) && str.length > 2 && str.length < 50;
};

/**
 * Transform debug-style role_used messages into user-friendly text
 */
export const transformRoleUsed = (roleUsed: string | null): RoleUsedInfo => {
  if (!roleUsed) return { text: 'N/A', type: 'fallback' };
  
  // Handle simple role names first (clean role names without technical patterns)
  if (isLikelyRoleName(roleUsed)) {
    return { 
      text: formatRoleName(roleUsed), 
      type: 'role',
      details: `Role-based routing: ${formatRoleName(roleUsed)}`
    };
  }
  
  // Handle default key success patterns
  if (roleUsed.includes('default_key') && roleUsed.includes('success')) {
    const attemptMatch = roleUsed.match(/attempt_(\d+)/);
    const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;
    return { 
      text: attempt === 1 ? 'Default Key' : `Default Key (Attempt ${attempt})`, 
      type: 'success',
      details: attempt > 1 ? `Required ${attempt} attempts to succeed` : undefined
    };
  }
  
  // Handle default key failure patterns
  if (roleUsed.includes('default_key') && roleUsed.includes('failed')) {
    const attemptMatch = roleUsed.match(/attempt_(\d+)/);
    const statusMatch = roleUsed.match(/status_(\w+)/);
    const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;
    const status = statusMatch ? statusMatch[1] : 'unknown';
    return { 
      text: `Failed (Attempt ${attempt})`, 
      type: 'error',
      details: `Failed with status: ${status}`
    };
  }
  
  // Handle multiple attempts failed
  if (roleUsed.includes('default_all') && roleUsed.includes('attempts_failed')) {
    const countMatch = roleUsed.match(/default_all_(\d+)_attempts/);
    const count = countMatch ? parseInt(countMatch[1]) : 0;
    return { 
      text: `All Keys Failed (${count} attempts)`, 
      type: 'error',
      details: `Tried ${count} different API keys, all failed`
    };
  }
  
  // Handle enhanced complexity-based routing with proximal search details
  if (roleUsed.includes('complexity_rr_clsf_') || roleUsed.includes('complexity_level_')) {
    // Enhanced pattern: complexity_rr_clsf_3_used_lvl_4_key_selected
    const enhancedMatch = roleUsed.match(/complexity_rr_clsf_(\d+)_used_lvl_(\d+)/);
    if (enhancedMatch) {
      const classifiedLevel = enhancedMatch[1];
      const usedLevel = enhancedMatch[2];
      if (classifiedLevel === usedLevel) {
        return {
          text: `Complexity Level ${usedLevel}`,
          type: 'success',
          details: `Classified and routed to complexity level ${usedLevel}`
        };
      } else {
        return {
          text: `Complexity ${classifiedLevel}→${usedLevel}`,
          type: 'success',
          details: `Classified as level ${classifiedLevel}, routed to available level ${usedLevel}`
        };
      }
    }
    
    // Simple pattern: complexity_level_3
    const levelMatch = roleUsed.match(/complexity_level_(\d+)/);
    if (levelMatch) {
      const level = levelMatch[1];
      return { 
        text: `Complexity Level ${level}`, 
        type: 'success',
        details: `Routed based on prompt complexity analysis`
      };
    }
  }
  
  // Handle strict fallback
  if (roleUsed.includes('fallback_position_')) {
    const posMatch = roleUsed.match(/fallback_position_(\d+)/);
    const position = posMatch ? parseInt(posMatch[1]) : 0;
    return { 
      text: `Fallback Key #${position + 1}`, 
      type: 'success',
      details: `Used fallback key at position ${position + 1}`
    };
  }
  
  // Handle intelligent role routing
  if (roleUsed.includes('intelligent_role_')) {
    const roleMatch = roleUsed.match(/intelligent_role_(.+)$/);
    const detectedRole = roleMatch ? roleMatch[1] : 'unknown';
    return {
      text: `Smart: ${formatRoleName(detectedRole)}`,
      type: 'role',
      details: `AI detected role: ${formatRoleName(detectedRole)}`
    };
  }
  
  // Enhanced fallback: Extract meaningful information from any unrecognized pattern
  return extractMeaningfulInfo(roleUsed);
};

/**
 * Extract meaningful information from unrecognized role_used patterns
 */
const extractMeaningfulInfo = (roleUsed: string): RoleUsedInfo => {
  // Try to extract complexity information
  const complexityMatch = roleUsed.match(/complexity[_\s]*(\d+)/i);
  if (complexityMatch) {
    const level = complexityMatch[1];
    return {
      text: `Complexity Level ${level}`,
      type: 'success',
      details: `Extracted complexity level ${level} from routing pattern`
    };
  }

  // Try to extract role names from complex patterns
  const roleNameMatch = extractRoleFromPattern(roleUsed);
  if (roleNameMatch) {
    return {
      text: formatRoleName(roleNameMatch),
      type: 'role',
      details: `Extracted role: ${formatRoleName(roleNameMatch)}`
    };
  }

  // Try to extract fallback information
  const fallbackMatch = roleUsed.match(/fallback[_\s]*(\d+)/i);
  if (fallbackMatch) {
    const position = parseInt(fallbackMatch[1]);
    return {
      text: `Fallback Key #${position + 1}`,
      type: 'success',
      details: `Extracted fallback position ${position + 1}`
    };
  }

  // Try to extract attempt information
  const attemptMatch = roleUsed.match(/attempt[_\s]*(\d+)/i);
  if (attemptMatch) {
    const attempt = parseInt(attemptMatch[1]);
    const isSuccess = roleUsed.toLowerCase().includes('success');
    const isFailed = roleUsed.toLowerCase().includes('fail');

    if (isSuccess) {
      return {
        text: attempt === 1 ? 'Default Key' : `Default Key (Attempt ${attempt})`,
        type: 'success',
        details: `Extracted success on attempt ${attempt}`
      };
    } else if (isFailed) {
      return {
        text: `Failed (Attempt ${attempt})`,
        type: 'error',
        details: `Extracted failure on attempt ${attempt}`
      };
    }
  }

  // Last resort: try to clean up the raw string for display
  const cleanedText = cleanRawRoleUsed(roleUsed);
  return {
    text: cleanedText,
    type: 'fallback',
    details: `Raw routing pattern: ${roleUsed}`
  };
};

/**
 * Extract role names from complex patterns
 */
const extractRoleFromPattern = (str: string): string | null => {
  // Look for patterns like "classified_as_ROLENAME_something"
  const classifiedMatch = str.match(/classified_as_([a-z_]+)_/i);
  if (classifiedMatch && isLikelyRoleName(classifiedMatch[1])) {
    return classifiedMatch[1];
  }

  // Look for patterns like "role_ROLENAME_something"
  const roleMatch = str.match(/role_([a-z_]+)_/i);
  if (roleMatch && isLikelyRoleName(roleMatch[1])) {
    return roleMatch[1];
  }

  // Look for patterns like "fb_role_ROLENAME"
  const fbRoleMatch = str.match(/fb_role_([a-z_]+)/i);
  if (fbRoleMatch && isLikelyRoleName(fbRoleMatch[1])) {
    return fbRoleMatch[1];
  }

  return null;
};

/**
 * Clean up raw role_used strings for display as last resort
 */
const cleanRawRoleUsed = (str: string): string => {
  // Remove common technical prefixes/suffixes
  let cleaned = str
    .replace(/^default_key_[a-f0-9-]+_/i, '')
    .replace(/_attempt_\d+$/i, '')
    .replace(/_status_\w+$/i, '')
    .replace(/_key_selected$/i, '')
    .replace(/_then_.*$/i, '')
    .replace(/^complexity_rr_/i, '')
    .replace(/_no_.*$/i, '');

  // If we cleaned it down to something reasonable, format it
  if (cleaned && cleaned.length > 2 && cleaned.length < 30 && isLikelyRoleName(cleaned)) {
    return formatRoleName(cleaned);
  }

  // Otherwise, just clean up the original string minimally
  return str
    .replace(/_/g, ' ')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
    .substring(0, 30) + (str.length > 30 ? '...' : '');
};

/**
 * Convert snake_case role names to Title Case
 */
export const formatRoleName = (roleName: string): string => {
  return roleName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

/**
 * Get CSS classes for role used badges based on type
 */
export const getRoleUsedBadgeClass = (type: 'success' | 'error' | 'fallback' | 'role'): string => {
  switch (type) {
    case 'role':
      return 'px-2 py-1 bg-blue-100 text-blue-800 rounded-lg text-xs font-medium';
    case 'success':
      return 'px-2 py-1 bg-green-100 text-green-800 rounded-lg text-xs font-medium';
    case 'error':
      return 'px-2 py-1 bg-red-100 text-red-800 rounded-lg text-xs font-medium';
    case 'fallback':
    default:
      return 'px-2 py-1 bg-orange-100 text-orange-800 rounded-lg text-xs font-medium';
  }
};

/**
 * Generate production-ready role_used strings for logging
 */
export const generateRoleUsedMessage = {
  // Default routing messages
  defaultKeySuccess: (attempt: number = 1) =>
    attempt === 1 ? 'default_key_success' : `default_key_success_attempt_${attempt}`,

  defaultKeyFailed: (attempt: number = 1, status?: string | number) =>
    `default_key_failed${status ? `_status_${status}` : ''}_attempt_${attempt}`,

  allKeysFailed: (attemptCount: number) =>
    `default_all_${attemptCount}_attempts_failed`,

  // Role-based routing messages
  roleRouting: (roleName: string) => roleName,

  intelligentRoleRouting: (detectedRole: string) =>
    `intelligent_role_${detectedRole}`,

  // Complexity-based routing messages
  complexityRouting: (level: number, keyIndex?: number) =>
    keyIndex !== undefined
      ? `complexity_level_${level}_key_${keyIndex}`
      : `complexity_level_${level}`,

  // Strict fallback routing messages
  fallbackRouting: (position: number) =>
    `fallback_position_${position}`,

  // Error states
  noKeysAvailable: () => 'no_keys_available',
  configurationError: () => 'configuration_error',
  routingStrategyError: (strategy: string) => `routing_strategy_error_${strategy}`,
};

/**
 * Transform provider names to user-friendly display names
 */
export const formatProviderName = (provider: string | null): string => {
  if (!provider) return 'N/A';

  const providerMap: Record<string, string> = {
    'openai': 'OpenAI',
    'anthropic': 'Anthropic',
    'google': 'Google',
    'openrouter': 'OpenRouter',
    'deepseek': 'DeepSeek',
    'xai': 'xAI',
  };

  return providerMap[provider.toLowerCase()] || provider;
};

/**
 * Transform model names to user-friendly display names
 */
export const formatModelName = (modelName: string | null): string => {
  if (!modelName) return 'N/A';

  // Remove common prefixes and make more readable
  return modelName
    .replace(/^(gpt-|claude-|gemini-|meta-llama\/|deepseek-|grok-)/, '')
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};
