'use client';

import React from 'react';
import { getSpecialistPersonality } from '@/config/specialistPersonalities';

interface TypingIndicatorProps {
  roleId: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({ roleId }) => {
  const personality = getSpecialistPersonality(roleId);
  
  if (!personality) {
    return null;
  }

  const getColorClasses = (color: string) => {
    const colorMap: { [key: string]: { bg: string; border: string; text: string; avatar: string } } = {
      purple: {
        bg: 'bg-purple-50',
        border: 'border-purple-200',
        text: 'text-purple-700',
        avatar: 'bg-purple-500'
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-700',
        avatar: 'bg-green-500'
      },
      orange: {
        bg: 'bg-orange-50',
        border: 'border-orange-200',
        text: 'text-orange-700',
        avatar: 'bg-orange-500'
      },
      indigo: {
        bg: 'bg-indigo-50',
        border: 'border-indigo-200',
        text: 'text-indigo-700',
        avatar: 'bg-indigo-500'
      },
      teal: {
        bg: 'bg-teal-50',
        border: 'border-teal-200',
        text: 'text-teal-700',
        avatar: 'bg-teal-500'
      },
      gray: {
        bg: 'bg-gray-50',
        border: 'border-gray-200',
        text: 'text-gray-700',
        avatar: 'bg-gray-500'
      }
    };
    
    return colorMap[color] || colorMap.gray;
  };

  const colors = getColorClasses(personality.color);

  return (
    <div className="flex space-x-3 animate-fade-in">
      {/* Avatar */}
      <div className={`flex-shrink-0 w-10 h-10 rounded-full ${colors.avatar} flex items-center justify-center text-white font-semibold shadow-md animate-pulse`}>
        <span className="text-lg">{personality.avatar}</span>
      </div>
      
      {/* Typing Bubble */}
      <div className="flex-1 max-w-4xl">
        {/* Header */}
        <div className="flex items-center space-x-2 mb-1">
          <span className={`font-semibold ${colors.text}`}>
            {personality.name}
          </span>
          <span className="text-xs text-gray-500">is thinking...</span>
        </div>
        
        {/* Typing Animation */}
        <div className={`${colors.bg} ${colors.border} border rounded-lg p-4 shadow-sm`}>
          <div className="flex items-center space-x-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-sm text-gray-600 italic">Working on it...</span>
          </div>
        </div>
      </div>
    </div>
  );
};
