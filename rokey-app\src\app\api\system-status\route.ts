import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientOnRequest();

    // Phase 2A Optimization: Run all health checks in parallel for much faster response
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const [apiGatewayResult, routingEngineResult, analyticsResult] = await Promise.allSettled([
      // API Gateway check
      supabase
        .from('custom_api_configs')
        .select('id')
        .limit(1),

      // Routing Engine check
      supabase
        .from('api_keys')
        .select('id')
        .eq('status', 'active')
        .limit(1),

      // Analytics check
      supabase
        .from('request_logs')
        .select('id')
        .gte('request_timestamp', oneDayAgo.toISOString())
        .limit(1)
    ]);

    const checks = [];

    // Process API Gateway result
    let apiGatewayStatus = 'operational';
    let apiGatewayDetails = '';
    if (apiGatewayResult.status === 'rejected') {
      apiGatewayStatus = 'down';
      apiGatewayDetails = apiGatewayResult.reason?.message || 'Connection failed';
    } else if (apiGatewayResult.value.error) {
      apiGatewayStatus = 'down';
      apiGatewayDetails = apiGatewayResult.value.error.message;
    }

    checks.push({
      name: 'API Gateway',
      status: apiGatewayStatus,
      details: apiGatewayDetails,
      lastChecked: new Date().toISOString()
    });

    // Process Routing Engine result
    let routingEngineStatus = 'operational';
    let routingEngineDetails = '';
    if (routingEngineResult.status === 'rejected') {
      routingEngineStatus = 'down';
      routingEngineDetails = routingEngineResult.reason?.message || 'Connection failed';
    } else if (routingEngineResult.value.error) {
      routingEngineStatus = 'degraded';
      routingEngineDetails = 'Error checking active keys';
    } else if (!routingEngineResult.value.data || routingEngineResult.value.data.length === 0) {
      routingEngineStatus = 'degraded';
      routingEngineDetails = 'No active API keys found';
    }

    checks.push({
      name: 'Routing Engine',
      status: routingEngineStatus,
      details: routingEngineDetails,
      lastChecked: new Date().toISOString()
    });

    // Process Analytics result
    let analyticsStatus = 'operational';
    let analyticsDetails = '';
    if (analyticsResult.status === 'rejected') {
      analyticsStatus = 'down';
      analyticsDetails = analyticsResult.reason?.message || 'Connection failed';
    } else if (analyticsResult.value.error) {
      analyticsStatus = 'degraded';
      analyticsDetails = 'Error checking recent logs';
    } else if (!analyticsResult.value.data || analyticsResult.value.data.length === 0) {
      analyticsStatus = 'degraded';
      analyticsDetails = 'No recent activity logged';
    }

    checks.push({
      name: 'Analytics',
      status: analyticsStatus,
      details: analyticsDetails,
      lastChecked: new Date().toISOString()
    });

    // Calculate overall system health
    const hasDown = checks.some(check => check.status === 'down');
    const hasDegraded = checks.some(check => check.status === 'degraded');
    
    let overallStatus = 'operational';
    if (hasDown) {
      overallStatus = 'down';
    } else if (hasDegraded) {
      overallStatus = 'degraded';
    }

    const response = NextResponse.json({
      overall_status: overallStatus,
      checks,
      last_updated: new Date().toISOString()
    });

    // Phase 2A Optimization: Add caching for system status
    response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');
    response.headers.set('X-Content-Type-Options', 'nosniff');

    return response;

  } catch (error: any) {
    console.error('Error in /api/system-status:', error);
    return NextResponse.json({
      overall_status: 'down',
      checks: [
        {
          name: 'System Check',
          status: 'down',
          details: 'Failed to perform system health checks',
          lastChecked: new Date().toISOString()
        }
      ],
      last_updated: new Date().toISOString(),
      error: error.message
    }, { status: 500 });
  }
}
