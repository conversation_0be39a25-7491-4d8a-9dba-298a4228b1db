'use client';

import React, { useState } from 'react';
import { ChatroomMessage } from '@/utils/orchestrationUtils';
import { 
  ClockIcon,
  EyeIcon,
  EyeSlashIcon,
  MegaphoneIcon,
  CheckCircleIcon,
  SparklesIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

interface ModeratorMessageProps {
  message: ChatroomMessage;
}

export const ModeratorMessage: React.FC<ModeratorMessageProps> = ({ message }) => {
  const [showFullContent, setShowFullContent] = useState(false);
  
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageIcon = () => {
    switch (message.messageType) {
      case 'assignment':
        return <MegaphoneIcon className="w-4 h-4" />;
      case 'final_output':
        return <CheckCircleIcon className="w-4 h-4" />;
      default:
        return <UserGroupIcon className="w-4 h-4" />;
    }
  };

  const getMessageStyle = () => {
    switch (message.messageType) {
      case 'assignment':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-700',
          avatar: 'bg-blue-500'
        };
      case 'final_output':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-700',
          avatar: 'bg-green-500'
        };
      default:
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-700',
          avatar: 'bg-blue-500'
        };
    }
  };

  const style = getMessageStyle();
  const shouldTruncate = message.content.length > 400;
  const displayContent = shouldTruncate && !showFullContent 
    ? message.content.substring(0, 400) + '...'
    : message.content;

  const hasCodeBlocks = message.content.includes('```');
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  
  const renderContent = () => {
    if (!hasCodeBlocks) {
      return (
        <div className="whitespace-pre-wrap text-gray-800">
          {displayContent}
        </div>
      );
    }

    // Split content by code blocks
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(message.content)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: message.content.substring(lastIndex, match.index)
        });
      }
      
      // Add code block
      parts.push({
        type: 'code',
        language: match[1] || 'text',
        content: match[2]
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add remaining text
    if (lastIndex < message.content.length) {
      parts.push({
        type: 'text',
        content: message.content.substring(lastIndex)
      });
    }

    return (
      <div className="space-y-3">
        {parts.map((part, index) => (
          part.type === 'code' ? (
            <div key={index} className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-400 uppercase tracking-wide">
                  {part.language}
                </span>
              </div>
              <pre className="text-sm text-gray-100 overflow-x-auto">
                <code>{part.content}</code>
              </pre>
            </div>
          ) : (
            <div key={index} className="whitespace-pre-wrap text-gray-800">
              {shouldTruncate && !showFullContent && index === parts.length - 1
                ? part.content.substring(0, Math.max(0, 400 - parts.slice(0, index).join('').length)) + '...'
                : part.content
              }
            </div>
          )
        ))}
      </div>
    );
  };

  return (
    <div className="flex space-x-3 animate-fade-in">
      {/* Avatar */}
      <div className={`flex-shrink-0 w-12 h-12 rounded-full ${style.avatar} flex items-center justify-center text-white font-bold shadow-lg border-2 border-white`}>
        <span className="text-xl">🎯</span>
      </div>
      
      {/* Message Content */}
      <div className="flex-1 max-w-4xl">
        {/* Header */}
        <div className="flex items-center space-x-2 mb-1">
          <span className={`font-bold ${style.text} text-lg`}>
            Alex (Moderator)
          </span>
          <div className="flex items-center space-x-1 text-gray-500">
            {getMessageIcon()}
            <span className="text-xs">
              {formatTime(message.timestamp)}
            </span>
          </div>
          {message.metadata?.stepNumber !== undefined && (
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
              {message.metadata.stepNumber === 0 ? 'Planning' : 
               message.metadata.stepNumber === 999 ? 'Synthesis' :
               message.metadata.stepNumber === 1000 ? 'Complete' :
               `Step ${message.metadata.stepNumber}`}
            </span>
          )}
        </div>
        
        {/* Message Bubble */}
        <div className={`${style.bg} ${style.border} border-2 rounded-lg p-4 shadow-md relative`}>
          {/* Special styling for final output */}
          {message.messageType === 'final_output' && (
            <div className="absolute -top-2 -right-2">
              <div className="bg-green-500 text-white rounded-full p-2 shadow-lg">
                <SparklesIcon className="w-4 h-4" />
              </div>
            </div>
          )}
          
          {renderContent()}
          
          {/* Show More/Less Button */}
          {shouldTruncate && (
            <button
              onClick={() => setShowFullContent(!showFullContent)}
              className="mt-3 flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 transition-colors font-medium"
            >
              {showFullContent ? (
                <>
                  <EyeSlashIcon className="w-4 h-4" />
                  <span>Show less</span>
                </>
              ) : (
                <>
                  <EyeIcon className="w-4 h-4" />
                  <span>Show more</span>
                </>
              )}
            </button>
          )}
        </div>
        
        {/* Special indicator for assignments */}
        {message.messageType === 'assignment' && message.content.includes('@') && (
          <div className="mt-2 text-xs text-gray-600 italic">
            📋 Task assignment in progress...
          </div>
        )}
      </div>
    </div>
  );
};
