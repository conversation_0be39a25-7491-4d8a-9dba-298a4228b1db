import json
import os
from supabase import create_client, Client

# --- CONFIGURATION - IMPORTANT: SET THESE ---
SUPABASE_URL = "https://hpkzzhpufhbxtxqaugjh.supabase.co"  # Replace with your Supabase project URL
# Replace with your Supabase key.
# If using anon key, <PERSON><PERSON> must allow insert on public.models for anon role.
# If using service_role key, be very careful with it - <PERSON><PERSON> is bypassed.
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8" 
INPUT_FILE = "openrouter_models_raw.json"
BATCH_SIZE = 50 # Insert models in batches of this size
# --- END CONFIGURATION ---


# --- Provider Mapping and Configuration (same as before) ---
PROVIDER_CONFIG = {
    "openai": {"id": "openai", "name": "OpenAI"},
    "google": {"id": "google", "name": "Google"},
    "anthropic": {"id": "anthropic", "name": "Anthropic"},
    "cohere": {"id": "cohere", "name": "Cohere"},
    "mistralai": {"id": "mistral-ai", "name": "Mistral AI"},
    "meta-llama": {"id": "meta", "name": "Meta AI"},
    "deepseek": {"id": "deepseek", "name": "DeepSeek"},
    "perplexity": {"id": "perplexity", "name": "Perplexity"},
    "xai": {"id": "xai", "name": "xAI (Grok)"}, # Original entry, will be superseded by "x-ai" if slug is hyphenated
    "fireworks": {"id": "fireworks-ai", "name": "Fireworks AI"},
    "togethercomputer": {"id": "together-ai", "name": "Together AI"},
    "teknium": {"id": "teknium", "name": "Teknium"},
    "nousresearch": {"id": "nous-research", "name": "Nous Research"},
    "recursal": {"id": "recursal", "name": "Recursal"},
    "microsoft": {"id": "microsoft", "name": "Microsoft"},
    "gryphe": {"id": "gryphe", "name": "Gryphe"},
    "huggingfaceh4": {"id": "huggingface-h4", "name": "HuggingFaceH4"},
    "liuhaotian": {"id": "liuhaotian", "name": "Liu Haotian"},
    "neversleep": {"id": "neversleep", "name": "NeverSleep"},
    "nvidia": {"id": "nvidia", "name": "NVIDIA NIM Models"},
    "01-ai": {"id": "yi", "name": "Yi Models"},
    "qwen": {"id": "alibaba", "name": "Alibaba (Qwen)"},
    "alibaba": {"id": "alibaba", "name": "Alibaba (Qwen)"}, # Handles if slug is 'alibaba'
    "openrouter": {"id": "openrouter", "name": "OpenRouter"},

    # Added based on your unmapped provider slugs:
    "aion-labs": {"id": "aion-labs", "name": "AION Labs"},
    "liquid": {"id": "liquid", "name": "Liquid"},
    "minimax": {"id": "minimax", "name": "Minimax"},
    "sao10k": {"id": "sao10k", "name": "Sao10k"},
    "eva-unit-01": {"id": "eva-unit-01", "name": "EVA Unit 01"}, # You might want a more generic 'id' if they provide many models
    "x-ai": {"id": "xai", "name": "xAI (Grok)"}, # Corrected key to match OpenRouter slug "x-ai"
    "amazon": {"id": "amazon", "name": "Amazon"},
    "infermatic": {"id": "infermatic", "name": "Infermatic"},
    "raifle": {"id": "raifle", "name": "Raifle"},
    "thedrummer": {"id": "thedrummer", "name": "TheDrummer"},
    "anthracite-org": {"id": "anthracite-org", "name": "Anthracite Org"},
    "inflection": {"id": "inflection", "name": "Inflection AI"},
    "aetherwiing": {"id": "aetherwiing", "name": "AetherWiing"},
    "nothingiisreal": {"id": "nothingiisreal", "name": "NothingIISReal"},
    "alpindale": {"id": "alpindale", "name": "Alpindale"},
    "cognitivecomputations": {"id": "cognitivecomputations", "name": "Cognitive Computations"},
    "sophosympatheia": {"id": "sophosympatheia", "name": "Sophosympatheia"},
    "undi95": {"id": "undi95", "name": "Undi95"},
    "pygmalionai": {"id": "pygmalionai", "name": "PygmalionAI"}, # Changed from "pygmalion" to match slug for key
    "mancer": {"id": "mancer", "name": "Mancer"},
}

def infer_family(model_id_str, model_name_str):
    lower_name = model_name_str.lower()
    lower_id = model_id_str.lower()
    if "gpt-4" in lower_name or "gpt-4" in lower_id: return "GPT-4"
    if "gpt-3.5" in lower_name or "gpt-3.5" in lower_id: return "GPT-3.5"
    if "o1" in lower_name or "o1" in lower_id: return "o1"
    if "claude 3.5" in lower_name: return "Claude 3.5"
    if "claude 3" in lower_name: return "Claude 3"
    if "claude 2" in lower_name: return "Claude 2"
    if "claude instant" in lower_name: return "Claude Instant"
    if "claude" in lower_name: return "Claude"
    if "gemini" in lower_name: return "Gemini"
    if "gemma" in lower_name: return "Gemma"
    if "command r" in lower_name: return "Command R"
    if "command" in lower_name: return "Command"
    if "llama" in lower_name: return "Llama"
    if "mistral" in lower_name or "mixtral" in lower_name: return "Mistral"
    if "grok" in lower_name: return "Grok"
    if "deepseek" in lower_name: return "DeepSeek"
    if "qwen" in lower_name: return "Qwen"
    if "sonar" in lower_name: return "Sonar"
    if "falcon" in lower_name: return "Falcon"
    if "phi" in lower_name: return "Phi"
    if "yi-" in lower_id: return "Yi"
    return model_name_str.split(" ")[0].split(":")[0].strip()

def infer_modality(architecture):
    if not architecture or not isinstance(architecture, dict):
        return 'unknown'
    input_modalities = [m.lower() for m in architecture.get("input_modalities", [])]
    output_modalities = [m.lower() for m in architecture.get("output_modalities", [])]
    has_text_input = "text" in input_modalities
    has_image_input = "image" in input_modalities
    has_audio_input = "audio" in input_modalities
    has_text_output = "text" in output_modalities
    has_image_output = "image" in output_modalities
    if "embedding" in input_modalities or "embedding" in output_modalities: return 'embedding'
    if "moderation" in input_modalities or "moderation" in output_modalities: return 'moderation'
    if has_text_input and has_image_input and has_text_output: return 'multimodal'
    if has_text_input and has_audio_input and has_text_output: return 'multimodal' 
    if has_text_input and has_text_output and not has_image_input and not has_audio_input and not has_image_output: return 'text'
    if has_image_input and has_image_output and not has_text_input and not has_text_output: return 'image'
    if has_text_input and has_audio_input and not has_text_output : return 'audio'
    if has_audio_input and has_text_output and not has_text_input: return 'audio'
    if len(input_modalities) > 1 and has_text_output: return 'multimodal'
    if has_text_input and has_text_output: return 'text'
    if has_image_input: return 'multimodal'
    return 'unknown'

def process_and_insert_models():
    if SUPABASE_URL == "YOUR_SUPABASE_URL" or SUPABASE_KEY == "YOUR_SUPABASE_KEY":
        print("ERROR: Please configure SUPABASE_URL and SUPABASE_KEY in the script.")
        return

    try:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("Successfully connected to Supabase.")
    except Exception as e:
        print(f"ERROR: Could not connect to Supabase: {e}")
        return

    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
    except FileNotFoundError:
        print(f"ERROR: Input file '{INPUT_FILE}' not found.")
        return
    except json.JSONDecodeError:
        print(f"ERROR: Could not decode JSON from '{INPUT_FILE}'.")
        return

    if 'data' not in raw_data or not isinstance(raw_data['data'], list):
        print("ERROR: Raw data does not contain a 'data' array.")
        return

    models_to_insert = []
    print(f"Processing {len(raw_data['data'])} models from OpenRouter...")

    for model_data in raw_data['data']:
        openrouter_model_id = model_data.get("id")
        if not openrouter_model_id:
            print(f"Skipping model with missing ID: {model_data.get('name')}")
            continue

        model_name = model_data.get("name", openrouter_model_id)
        description = model_data.get("description")
        context_length = model_data.get("context_length")
        architecture = model_data.get("architecture", {})
        pricing = model_data.get("pricing", {})
        top_provider_info = model_data.get("top_provider", {})
        max_completion_tokens = top_provider_info.get("max_completion_tokens") if isinstance(top_provider_info, dict) else None
        
        provider_slug_from_id = openrouter_model_id.split('/')[0] if '/' in openrouter_model_id else "unknown"
        provider_conf = PROVIDER_CONFIG.get(provider_slug_from_id)
        db_provider_id = provider_conf["id"] if provider_conf else provider_slug_from_id
        
        if not provider_conf:
             print(f"Notice: Using direct slug '{db_provider_id}' for unmapped provider from model '{openrouter_model_id}'. Ensure '{db_provider_id}' exists in your public.providers table.")

        family = infer_family(openrouter_model_id, model_name)
        modality = infer_modality(architecture)

        supabase_model_entry = {
            "id": openrouter_model_id,
            "name": model_name,
            "display_name": model_name,
            "provider_id": db_provider_id,
            "description": description,
            "version": None,
            "family": family,
            "input_token_limit": context_length if context_length is not None else None, # Ensure NULL for DB
            "output_token_limit": max_completion_tokens if max_completion_tokens is not None else None, # Ensure NULL
            "context_window": context_length if context_length is not None else None, # Ensure NULL
            "modality": modality,
            "is_public": True,
            "provider_specific_details": { # Store as JSONB
                "openrouter_id": openrouter_model_id,
                "architecture": architecture,
                "pricing": pricing,
                "top_provider_info": top_provider_info,
                "hugging_face_id": model_data.get("hugging_face_id")
            },
            "model_metadata": {"source": "openrouter_v1_models_list"} # Store as JSONB
        }
        # Filter out None values for fields that are nullable in DB, to avoid sending 'None' string
        supabase_model_entry_cleaned = {k: v for k, v in supabase_model_entry.items() if v is not None}

        models_to_insert.append(supabase_model_entry_cleaned)

    if not models_to_insert:
        print("No models processed to insert.")
        return

    try:
        # Step 1: Delete all existing models
        print("Deleting existing models from public.models table...")
        # The delete operation without any further conditions will delete all rows.
        # Match on a dummy condition that is always true if .delete() needs a condition,
        # or use .rpc() for a function if direct table delete isn't straightforward.
        # A common pattern for "delete all" is to select IDs and then delete by IDs,
        # but for a full clear, a direct delete is better if allowed.
        # Supabase client typically requires a filter for delete, so we fetch all IDs first.
        
        all_current_models_response = supabase.table("models").select("id").execute()
        if all_current_models_response.data:
            ids_to_delete = [m["id"] for m in all_current_models_response.data]
            print(f"Found {len(ids_to_delete)} existing models to delete.")
            if ids_to_delete:
                 delete_response = supabase.table("models").delete().in_("id", ids_to_delete).execute()
                 print(f"Deletion response: {delete_response}")
                 if hasattr(delete_response, 'error') and delete_response.error:
                     print(f"Error deleting models: {delete_response.error}")
                     # return # Optionally stop if deletion fails critically
                 else:
                     print(f"Successfully deleted {len(delete_response.data) if delete_response.data else 'some/all'} existing models.")
        else:
            print("No existing models found to delete.")


        # Step 2: Insert new models in batches
        print(f"Inserting {len(models_to_insert)} new models in batches of {BATCH_SIZE}...")
        for i in range(0, len(models_to_insert), BATCH_SIZE):
            batch = models_to_insert[i:i + BATCH_SIZE]
            print(f"Inserting batch {i // BATCH_SIZE + 1}...")
            data, error = supabase.table("models").insert(batch).execute()
            
            # The execute() method for insert in supabase-py v1 might return a tuple (response, error_object)
            # or an object that has a .data and .error attribute.
            # For supabase-py v2 (which create_client typically uses), it's an APIResponse object.
            
            current_error = None
            if isinstance(data, tuple): # older supabase-py might return (response, error_obj)
                 if len(data) > 1 and data[1]:
                      current_error = data[1]
            elif hasattr(data, 'error') and data.error: # newer supabase-py APIResponse
                 current_error = data.error
            
            if current_error:
                print(f"ERROR inserting batch: {current_error}")
                # Consider how to handle partial failures. For now, it prints and continues.
            else:
                print(f"Successfully inserted batch {i // BATCH_SIZE + 1}.")
        
        print("Model processing and insertion complete.")

    except Exception as e:
        print(f"An error occurred during Supabase operations: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # IMPORTANT: Ensure you have installed supabase-py: pip install supabase-py
    # Before running:
    # 1. Fill in YOUR_SUPABASE_URL and YOUR_SUPABASE_KEY at the top of this script.
    # 2. Ensure the 'openrouter_models_raw.json' file is in the same directory as this script.
    # 3. Ensure all provider_id values that will be inserted exist in your 'public.providers' Supabase table.
    #    The script will print notices for unmapped provider slugs from OpenRouter model IDs.
    process_and_insert_models()