'use client';

import React from 'react';
import { 
  ArrowRightIcon,
  CheckCircleIcon,
  ClockIcon,
  CpuChipIcon,
  ExclamationTriangleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface ModelStatus {
  roleId: string;
  modelName: string;
  status: 'waiting' | 'assigned' | 'working' | 'completed' | 'failed';
  progress: number;
  output?: string;
  duration?: number;
  quality?: number;
}

interface OrchestrationProgressProps {
  modelStatuses: ModelStatus[];
  currentPhase: 'planning' | 'executing' | 'synthesizing' | 'complete';
  overallProgress: number;
}

export const OrchestrationProgress: React.FC<OrchestrationProgressProps> = ({
  modelStatuses,
  currentPhase,
  overallProgress
}) => {
  const formatRoleName = (roleId: string) => {
    return roleId
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'waiting':
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
      case 'assigned':
        return <ClockIcon className="w-4 h-4 text-blue-500" />;
      case 'working':
        return <CpuChipIcon className="w-4 h-4 text-orange-500 animate-pulse" />;
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'bg-gray-100 border-gray-300';
      case 'assigned':
        return 'bg-blue-50 border-blue-300';
      case 'working':
        return 'bg-orange-50 border-orange-300';
      case 'completed':
        return 'bg-green-50 border-green-300';
      case 'failed':
        return 'bg-red-50 border-red-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  };

  const getConnectorColor = (currentStatus: string, nextStatus: string) => {
    if (currentStatus === 'completed') {
      return 'bg-green-500';
    } else if (currentStatus === 'working') {
      return 'bg-orange-500';
    } else if (currentStatus === 'assigned') {
      return 'bg-blue-500';
    }
    return 'bg-gray-300';
  };

  return (
    <div className="space-y-6">
      {/* Workflow Visualization */}
      {modelStatuses.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-semibold text-gray-900 mb-4">Workflow Progress</h4>
          
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            {modelStatuses.map((model, index) => (
              <React.Fragment key={model.roleId}>
                {/* Step */}
                <div className="flex-shrink-0">
                  <div className={`relative p-3 rounded-lg border-2 min-w-[120px] ${getStepColor(model.status)}`}>
                    <div className="flex items-center justify-between mb-2">
                      {getStatusIcon(model.status)}
                      <span className="text-xs font-medium text-gray-600">
                        Step {index + 1}
                      </span>
                    </div>
                    
                    <h5 className="text-xs font-semibold text-gray-900 mb-1">
                      {formatRoleName(model.roleId)}
                    </h5>
                    
                    <p className="text-xs text-gray-600 truncate">
                      {model.modelName}
                    </p>
                    
                    {/* Progress bar for working status */}
                    {model.status === 'working' && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div 
                            className="bg-orange-500 h-1 rounded-full transition-all duration-500"
                            style={{ width: `${model.progress * 100}%` }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {/* Quality indicator for completed */}
                    {model.status === 'completed' && model.quality && (
                      <div className="mt-2 flex items-center space-x-1">
                        <SparklesIcon className="w-3 h-3 text-green-500" />
                        <span className="text-xs text-green-600">
                          {Math.round(model.quality * 100)}%
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Connector Arrow */}
                {index < modelStatuses.length - 1 && (
                  <div className="flex-shrink-0 flex items-center">
                    <div className={`w-8 h-0.5 ${getConnectorColor(model.status, modelStatuses[index + 1].status)}`} />
                    <ArrowRightIcon className="w-4 h-4 text-gray-400 ml-1" />
                  </div>
                )}
              </React.Fragment>
            ))}
            
            {/* Synthesis Step */}
            {currentPhase === 'synthesizing' || currentPhase === 'complete' && (
              <>
                <div className="flex-shrink-0 flex items-center">
                  <div className="w-8 h-0.5 bg-purple-500" />
                  <ArrowRightIcon className="w-4 h-4 text-gray-400 ml-1" />
                </div>
                
                <div className="flex-shrink-0">
                  <div className={`relative p-3 rounded-lg border-2 min-w-[120px] ${
                    currentPhase === 'complete' 
                      ? 'bg-green-50 border-green-300' 
                      : 'bg-purple-50 border-purple-300'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      {currentPhase === 'complete' ? (
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      ) : (
                        <SparklesIcon className="w-4 h-4 text-purple-500 animate-spin" />
                      )}
                      <span className="text-xs font-medium text-gray-600">
                        Final
                      </span>
                    </div>
                    
                    <h5 className="text-xs font-semibold text-gray-900 mb-1">
                      Synthesis
                    </h5>
                    
                    <p className="text-xs text-gray-600">
                      {currentPhase === 'complete' ? 'Complete' : 'Combining outputs'}
                    </p>
                    
                    {currentPhase === 'synthesizing' && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div className="bg-purple-500 h-1 rounded-full animate-pulse w-3/4" />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Phase Indicators */}
      <div className="grid grid-cols-4 gap-2">
        {[
          { phase: 'planning', label: 'Planning', icon: ClockIcon },
          { phase: 'executing', label: 'Executing', icon: CpuChipIcon },
          { phase: 'synthesizing', label: 'Synthesizing', icon: SparklesIcon },
          { phase: 'complete', label: 'Complete', icon: CheckCircleIcon }
        ].map(({ phase, label, icon: Icon }) => {
          const isActive = currentPhase === phase;
          const isCompleted = ['planning', 'executing', 'synthesizing', 'complete'].indexOf(currentPhase) > 
                            ['planning', 'executing', 'synthesizing', 'complete'].indexOf(phase);
          
          return (
            <div
              key={phase}
              className={`p-3 rounded-lg border text-center transition-all duration-300 ${
                isActive 
                  ? 'bg-blue-50 border-blue-300 text-blue-700' 
                  : isCompleted
                  ? 'bg-green-50 border-green-300 text-green-700'
                  : 'bg-gray-50 border-gray-200 text-gray-500'
              }`}
            >
              <Icon className={`w-5 h-5 mx-auto mb-1 ${
                isActive ? 'animate-pulse' : ''
              }`} />
              <p className="text-xs font-medium">{label}</p>
            </div>
          );
        })}
      </div>

      {/* Statistics */}
      {modelStatuses.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-bold text-gray-900">
              {modelStatuses.length}
            </p>
            <p className="text-xs text-gray-600">Total Steps</p>
          </div>
          
          <div className="p-3 bg-green-50 rounded-lg">
            <p className="text-lg font-bold text-green-700">
              {modelStatuses.filter(m => m.status === 'completed').length}
            </p>
            <p className="text-xs text-green-600">Completed</p>
          </div>
          
          <div className="p-3 bg-orange-50 rounded-lg">
            <p className="text-lg font-bold text-orange-700">
              {modelStatuses.filter(m => m.status === 'working').length}
            </p>
            <p className="text-xs text-orange-600">In Progress</p>
          </div>
          
          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-lg font-bold text-blue-700">
              {Math.round(overallProgress)}%
            </p>
            <p className="text-xs text-blue-600">Progress</p>
          </div>
        </div>
      )}
    </div>
  );
};
