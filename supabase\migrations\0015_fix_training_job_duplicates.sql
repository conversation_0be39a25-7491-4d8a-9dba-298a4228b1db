-- Migration: Fix Training Job Duplicate Creation Issue
-- This migration adds safeguards to prevent duplicate training jobs for the same configuration
-- which was causing CASCADE DELETE of training files

-- 1. Add a unique constraint to prevent multiple training jobs per config
-- Note: We use a partial unique index to allow only one 'completed' job per config
-- This prevents the creation of duplicate training jobs that would trigger CASCADE DELETE

-- First, let's check if there are any duplicate completed training jobs
-- and consolidate them before adding the constraint

DO $$
DECLARE
    config_record RECORD;
    job_record RECORD;
    latest_job_id UUID;
    files_to_move RECORD;
BEGIN
    -- For each custom_api_config_id that has multiple completed training jobs
    FOR config_record IN 
        SELECT custom_api_config_id, COUNT(*) as job_count
        FROM training_jobs 
        WHERE status = 'completed'
        GROUP BY custom_api_config_id 
        HAVING COUNT(*) > 1
    LOOP
        RAISE NOTICE 'Found % completed training jobs for config %', config_record.job_count, config_record.custom_api_config_id;
        
        -- Get the latest (most recent) training job for this config
        SELECT id INTO latest_job_id
        FROM training_jobs 
        WHERE custom_api_config_id = config_record.custom_api_config_id 
          AND status = 'completed'
        ORDER BY created_at DESC 
        LIMIT 1;
        
        RAISE NOTICE 'Latest job ID for config %: %', config_record.custom_api_config_id, latest_job_id;
        
        -- Move all training files from older jobs to the latest job
        FOR files_to_move IN
            SELECT tf.id, tf.training_job_id, tj.created_at
            FROM training_files tf
            JOIN training_jobs tj ON tf.training_job_id = tj.id
            WHERE tj.custom_api_config_id = config_record.custom_api_config_id
              AND tj.status = 'completed'
              AND tf.training_job_id != latest_job_id
        LOOP
            RAISE NOTICE 'Moving training file % from job % to job %', files_to_move.id, files_to_move.training_job_id, latest_job_id;
            
            -- Update the training file to point to the latest job
            UPDATE training_files 
            SET training_job_id = latest_job_id,
                updated_at = NOW()
            WHERE id = files_to_move.id;
        END LOOP;
        
        -- Delete the older training jobs (files are now safe in the latest job)
        DELETE FROM training_jobs 
        WHERE custom_api_config_id = config_record.custom_api_config_id 
          AND status = 'completed'
          AND id != latest_job_id;
          
        RAISE NOTICE 'Consolidated training jobs for config % into job %', config_record.custom_api_config_id, latest_job_id;
    END LOOP;
END $$;

-- 2. Create a unique partial index to prevent future duplicates
-- This allows only one 'completed' training job per custom_api_config_id
CREATE UNIQUE INDEX IF NOT EXISTS idx_training_jobs_unique_completed_per_config 
ON training_jobs (custom_api_config_id) 
WHERE status = 'completed';

-- 3. Add a comment explaining the constraint
COMMENT ON INDEX idx_training_jobs_unique_completed_per_config IS 
'Ensures only one completed training job exists per API configuration to prevent CASCADE DELETE data loss';

-- 4. Create a function to safely upsert training jobs
-- This function will update existing jobs instead of creating duplicates
CREATE OR REPLACE FUNCTION upsert_training_job(
    p_custom_api_config_id UUID,
    p_name TEXT,
    p_description TEXT DEFAULT NULL,
    p_training_data JSONB DEFAULT NULL,
    p_parameters JSONB DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    custom_api_config_id UUID,
    name TEXT,
    description TEXT,
    status TEXT,
    training_data JSONB,
    parameters JSONB,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    is_new_job BOOLEAN
) AS $$
DECLARE
    existing_job_id UUID;
    result_record RECORD;
BEGIN
    -- Check if a completed training job already exists for this config
    SELECT tj.id INTO existing_job_id
    FROM training_jobs tj
    WHERE tj.custom_api_config_id = p_custom_api_config_id 
      AND tj.status = 'completed'
    LIMIT 1;
    
    IF existing_job_id IS NOT NULL THEN
        -- Update existing job
        UPDATE training_jobs 
        SET 
            description = COALESCE(p_description, description),
            training_data = COALESCE(p_training_data, training_data),
            parameters = COALESCE(p_parameters, parameters),
            updated_at = NOW()
        WHERE training_jobs.id = existing_job_id
        RETURNING training_jobs.* INTO result_record;
        
        -- Return the updated job with is_new_job = false
        RETURN QUERY SELECT 
            result_record.id,
            result_record.custom_api_config_id,
            result_record.name,
            result_record.description,
            result_record.status,
            result_record.training_data,
            result_record.parameters,
            result_record.created_at,
            result_record.updated_at,
            FALSE as is_new_job;
    ELSE
        -- Create new job
        INSERT INTO training_jobs (
            custom_api_config_id,
            name,
            description,
            training_data,
            parameters,
            status,
            progress_percentage,
            started_at,
            completed_at
        ) VALUES (
            p_custom_api_config_id,
            p_name,
            p_description,
            p_training_data,
            p_parameters,
            'completed',
            100,
            NOW(),
            NOW()
        ) RETURNING training_jobs.* INTO result_record;
        
        -- Return the new job with is_new_job = true
        RETURN QUERY SELECT 
            result_record.id,
            result_record.custom_api_config_id,
            result_record.name,
            result_record.description,
            result_record.status,
            result_record.training_data,
            result_record.parameters,
            result_record.created_at,
            result_record.updated_at,
            TRUE as is_new_job;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 5. Add comment for the function
COMMENT ON FUNCTION upsert_training_job IS 
'Safely creates or updates training jobs to prevent duplicates and CASCADE DELETE data loss';

-- Migration completed
SELECT 'Training job duplicate prevention migration completed successfully' as status;
