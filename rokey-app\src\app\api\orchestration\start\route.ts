import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Schema for the request body
const StartOrchestrationSchema = z.object({
  executionId: z.string().uuid(),
});

export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientOnRequest();
  
  try {
    // Parse and validate the request body
    const body = await request.json();
    const validatedBody = StartOrchestrationSchema.parse(body);
    
    // Get the execution record
    const { data: execution, error: executionError } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', validatedBody.executionId)
      .single();
      
    if (executionError || !execution) {
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }
    
    // Check if the execution is in a startable state
    if (execution.status !== 'pending' && execution.status !== 'in_progress') {
      return NextResponse.json(
        { error: `Execution is in ${execution.status} state, not startable` },
        { status: 400 }
      );
    }
    
    // Get the first step
    const { data: firstStep, error: stepError } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', validatedBody.executionId)
      .eq('step_number', 1)
      .single();
      
    if (stepError || !firstStep) {
      return NextResponse.json(
        { error: 'First step not found' },
        { status: 404 }
      );
    }
    
    // Update execution status to in_progress
    await supabase
      .from('orchestration_executions')
      .update({ status: 'in_progress' })
      .eq('id', validatedBody.executionId);
      
    // Update first step status to pending if it's not already
    if (firstStep.status !== 'pending') {
      await supabase
        .from('orchestration_steps')
        .update({ status: 'pending' })
        .eq('id', firstStep.id);
    }
    
    // In Phase 1, we'll just return a success response
    // In Phase 2, we'll implement a background worker to process the steps
    
    // For Phase 1, we'll process the first step immediately
    const processResponse = await fetch(`${request.nextUrl.origin}/api/orchestration/process-step`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        executionId: validatedBody.executionId,
        stepId: firstStep.id,
      }),
    });
    
    if (!processResponse.ok) {
      const errorData = await processResponse.json();
      return NextResponse.json(
        { error: 'Error processing first step', details: errorData },
        { status: processResponse.status }
      );
    }
    
    return NextResponse.json({
      success: true,
      execution: {
        id: execution.id,
        status: 'in_progress',
      }
    });
  } catch (error) {
    console.error(`[Start Orchestration] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
}