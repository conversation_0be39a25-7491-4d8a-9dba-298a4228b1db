'use client';

import React, { useState, useEffect } from 'react';
import { useOrchestrationStream } from '@/hooks/useOrchestrationStream';
import { OrchestrationProgress } from './OrchestrationProgress';
import { OrchestrationNarrator } from './OrchestrationNarrator';
import { ModelStatusCard } from './ModelStatusCard';
import { 
  CpuChipIcon, 
  SparklesIcon, 
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface AITeamOrchestratorProps {
  executionId: string;
  onComplete?: (result: string) => void;
  onError?: (error: string) => void;
}

interface ModelStatus {
  roleId: string;
  modelName: string;
  status: 'waiting' | 'assigned' | 'working' | 'completed' | 'failed';
  progress: number;
  output?: string;
  duration?: number;
  quality?: number;
}

export const AITeamOrchestrator: React.FC<AITeamOrchestratorProps> = ({
  executionId,
  onComplete,
  onError
}) => {
  const [modelStatuses, setModelStatuses] = useState<ModelStatus[]>([]);
  const [currentNarration, setCurrentNarration] = useState<string>('');
  const [overallProgress, setOverallProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [finalResult, setFinalResult] = useState<string>('');
  const [streamingResult, setStreamingResult] = useState<string>('');
  const [directStreamSource, setDirectStreamSource] = useState<EventSource | null>(null);
  const [orchestrationPhase, setOrchestrationPhase] = useState<'planning' | 'executing' | 'synthesizing' | 'complete'>('planning');
  const [synthesisStreamUrl, setSynthesisStreamUrl] = useState<string | null>(null);

  // Use the orchestration stream hook with synthesis stream URL support
  const {
    events,
    isConnected,
    error: streamError,
    lastEvent
  } = useOrchestrationStream(executionId, synthesisStreamUrl || undefined);

  // Process incoming events
  useEffect(() => {
    if (!lastEvent) return;

    const event = lastEvent;
    
    switch (event.type) {
      case 'orchestration_started':
        setCurrentNarration(event.data.commentary || '🎬 AI team is assembling...');
        setOrchestrationPhase('planning');
        break;

      case 'task_decomposed':
        setCurrentNarration(event.data.commentary || '📋 Breaking down the task into specialized steps...');
        // Initialize model statuses from the decomposition
        if (event.data.steps) {
          const statuses: ModelStatus[] = event.data.steps.map((step: any) => ({
            roleId: step.roleId,
            modelName: step.modelName || 'Unknown Model',
            status: 'waiting',
            progress: 0
          }));
          setModelStatuses(statuses);
        }
        break;

      case 'step_assigned':
        setCurrentNarration(event.data.commentary || `📋 Assigning ${event.role_id} specialist...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { ...model, status: 'assigned', modelName: event.model_name || model.modelName }
            : model
        ));
        break;

      case 'step_started':
        setCurrentNarration(event.data.commentary || `🚀 ${event.role_id} is starting work...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { ...model, status: 'working', progress: 0.1 }
            : model
        ));
        setOrchestrationPhase('executing');
        break;

      case 'step_progress':
        setCurrentNarration(event.data.commentary || `⚡ ${event.role_id} is making progress...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { 
                ...model, 
                progress: event.data.progress || 0.5,
                output: event.data.partialOutput || model.output
              }
            : model
        ));
        break;

      case 'step_completed':
        setCurrentNarration(event.data.commentary || `✅ ${event.role_id} completed their work!`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { 
                ...model, 
                status: 'completed', 
                progress: 1.0,
                output: event.data.output,
                duration: event.data.duration,
                quality: event.data.quality
              }
            : model
        ));
        break;

      case 'step_failed':
        setCurrentNarration(event.data.commentary || `❌ ${event.role_id} encountered an issue...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { ...model, status: 'failed', progress: 0 }
            : model
        ));
        if (onError) {
          onError(event.data.error || 'Step failed');
        }
        break;

      case 'synthesis_started':
        setCurrentNarration(event.data.commentary || '🧩 Combining all specialist outputs...');
        setOrchestrationPhase('synthesizing');

        // Debug: Log the synthesis_started event data
        console.log('[AITeamOrchestrator] synthesis_started event data:', event.data);
        console.log('[AITeamOrchestrator] directStreamUrl:', event.data.directStreamUrl);
        console.log('[AITeamOrchestrator] synthesisExecutionId:', event.data.synthesisExecutionId);

        // If we have a direct stream URL, update it for real-time streaming
        if (event.data.directStreamUrl) {
          console.log(`[AITeamOrchestrator] Updating to synthesis stream: ${event.data.directStreamUrl}`);
          setSynthesisStreamUrl(event.data.directStreamUrl);
        }

        // Legacy support: Check if we have synthesis execution ID for the old POST approach
        if (event.data.synthesisExecutionId && event.data.directStreamUrl === '/api/v1/chat/completions') {
          console.log(`[AITeamOrchestrator] Starting synthesis via chat completions endpoint`);
          console.log(`[AITeamOrchestrator] Synthesis execution ID: ${event.data.synthesisExecutionId}`);

          // Make a POST request to the chat completions endpoint for synthesis
          const synthesisUrl = window.location.origin + event.data.directStreamUrl;

          fetch(synthesisUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_ROKEY_API_ACCESS_TOKEN || 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13'}`,
              'X-Synthesis-Execution-Id': event.data.synthesisExecutionId,
            },
            body: JSON.stringify({
              messages: [{ role: 'user', content: 'synthesis_request' }],
              stream: true,
              custom_api_config_id: 'synthesis-internal'
            })
          })
          .then(response => {
            if (!response.ok) {
              throw new Error(`Synthesis request failed: ${response.status}`);
            }

            if (!response.body) {
              throw new Error('No response body for synthesis stream');
            }

            console.log(`[AITeamOrchestrator] Synthesis stream started successfully`);

            // Handle the streaming response
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let accumulatedText = '';

            const readStream = async () => {
              try {
                while (true) {
                  const { done, value } = await reader.read();
                  if (done) break;

                  const chunk = decoder.decode(value, { stream: true });
                  const lines = chunk.split('\n');

                  for (const line of lines) {
                    if (line.startsWith('data: ')) {
                      const data = line.slice(6);
                      if (data === '[DONE]') {
                        console.log(`[AITeamOrchestrator] Synthesis stream completed`);
                        setIsComplete(true);
                        setOrchestrationPhase('complete');
                        setFinalResult(accumulatedText);
                        if (onComplete) {
                          onComplete(accumulatedText);
                        }
                        return;
                      }

                      try {
                        const parsed = JSON.parse(data);
                        if (parsed.choices?.[0]?.delta?.content) {
                          const newContent = parsed.choices[0].delta.content;
                          accumulatedText += newContent;
                          setStreamingResult(accumulatedText);
                        }
                      } catch (parseError) {
                        console.warn('[AITeamOrchestrator] Failed to parse streaming chunk:', parseError);
                      }
                    }
                  }
                }
              } catch (streamError) {
                console.error('[AITeamOrchestrator] Error reading synthesis stream:', streamError);
              } finally {
                reader.releaseLock();
              }
            };

            readStream();
          })
          .catch(error => {
            console.error('[AITeamOrchestrator] Synthesis request failed:', error);
            // Fallback to old approach if needed
          });
        } else if (event.data.directStreamUrl && event.data.directStreamUrl !== '/api/v1/chat/completions') {
          // Fallback to old approach for backward compatibility (only for non-chat-completions URLs)
          const directStreamUrl = event.data.directStreamUrl.startsWith('http')
            ? event.data.directStreamUrl
            : window.location.origin + event.data.directStreamUrl;

          console.log(`[AITeamOrchestrator] Falling back to direct synthesis stream: ${directStreamUrl}`);

          // Connect to the direct streaming endpoint with credentials
          const eventSource = new EventSource(directStreamUrl, { withCredentials: true });
          
          // Log connection events
          eventSource.onopen = () => {
            console.log('[AITeamOrchestrator] Direct stream connection opened');
          };
          
          // Handle the direct stream
          eventSource.onmessage = (streamEvent) => {
            console.log('[AITeamOrchestrator] Received message from direct stream');
            console.log('[AITeamOrchestrator] Raw message data:', streamEvent.data.substring(0, 200));
            
            try {
              // Handle [DONE] messages
              if (!streamEvent.data) {
                return;
              }
              
              if (streamEvent.data === '[DONE]') {
                console.log('[AITeamOrchestrator] Received [DONE] message');
                
                // Mark as complete when we receive [DONE]
                // This ensures we don't have to wait for the orchestration_completed event
                if (streamingResult) {
                  setIsComplete(true);
                  setOrchestrationPhase('complete');
                  setFinalResult(streamingResult);
                  
                  if (onComplete) {
                    onComplete(streamingResult);
                  }
                }
                
                return;
              }
              
              // Parse the data as JSON
              const data = JSON.parse(streamEvent.data);
              
              // Log the data structure to help debug
              if (process.env.NODE_ENV === 'development') {
                console.log('[AITeamOrchestrator] Stream data structure:', 
                  JSON.stringify(data).substring(0, 100) + '...');
              }
              
              // Extract content from the delta (OpenAI format)
              if (data.choices && data.choices[0] && data.choices[0].delta) {
                const content = data.choices[0].delta.content || '';
                
                if (content) {
                  console.log(`[AITeamOrchestrator] Received content chunk: ${content.length} chars`);
                  // Update the streaming result with the new content
                  setStreamingResult(prev => prev + content);
                }
              }
              // Also handle Gemini's direct format
              else if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                const content = data.candidates[0].content.parts[0].text || '';
                
                if (content) {
                  console.log(`[AITeamOrchestrator] Received Gemini content: ${content.length} chars`);
                  // Update the streaming result with the new content
                  setStreamingResult(prev => prev + content);
                }
              }
            } catch (error) {
              console.error('[AITeamOrchestrator] Error parsing direct stream data:', error);
              console.log('Raw data:', streamEvent.data.substring(0, 100));
            }
          };
          
          // Handle stream completion
          eventSource.addEventListener('done', () => {
            console.log('[AITeamOrchestrator] Direct synthesis stream completed');
            eventSource.close();
            
            // Mark as complete when the stream is done
            setIsComplete(true);
            setOrchestrationPhase('complete');
          });
          
          // Handle stream errors
          eventSource.onerror = (error) => {
            console.error('[AITeamOrchestrator] Direct synthesis stream error:', error);
            
            // Try to fetch the error details
            fetch(directStreamUrl)
              .then(response => {
                if (!response.ok) {
                  return response.text().then(text => {
                    console.error(`[AITeamOrchestrator] Stream endpoint error: ${response.status}, ${text}`);
                  });
                }
                return response.text().then(text => {
                  console.log(`[AITeamOrchestrator] Stream endpoint response: ${text.substring(0, 100)}`);
                });
              })
              .catch(fetchError => {
                console.error('[AITeamOrchestrator] Error fetching stream endpoint:', fetchError);
              });
            
            // Close the stream
            eventSource.close();
            
            // Set an error message
            setCurrentNarration('❌ Error connecting to synthesis stream. Waiting for completion...');
            
            // Try a fallback approach - make a regular fetch request to get the synthesis
            console.log('[AITeamOrchestrator] Trying fallback approach...');
            
            // Make a regular fetch request to the same endpoint
            fetch(directStreamUrl.replace('synthesis-stream-direct', 'synthesis-fallback'))
              .then(response => {
                if (!response.ok) {
                  throw new Error(`Fallback request failed: ${response.status}`);
                }
                return response.json();
              })
              .then(data => {
                if (data.result) {
                  console.log('[AITeamOrchestrator] Fallback approach succeeded');
                  setStreamingResult(data.result);
                  setIsComplete(true);
                  setOrchestrationPhase('complete');
                  setFinalResult(data.result);
                  
                  if (onComplete) {
                    onComplete(data.result);
                  }
                }
              })
              .catch(fallbackError => {
                console.error('[AITeamOrchestrator] Fallback approach failed:', fallbackError);
              });
          };
          
          // Store the event source for cleanup
          setDirectStreamSource(eventSource);
          
          // Set a timeout to check if we're receiving data
          let streamTimeout: NodeJS.Timeout | null = setTimeout(() => {
            console.log('[AITeamOrchestrator] Direct stream timeout - no data received');
            streamTimeout = null;
            
            if (directStreamSource) {
              directStreamSource.close();
              setDirectStreamSource(null);
            }
            
            // If we have some streaming result already, use it
            if (streamingResult) {
              console.log('[AITeamOrchestrator] Using partial streaming result on timeout');
              setIsComplete(true);
              setOrchestrationPhase('complete');
              setFinalResult(streamingResult);
              
              if (onComplete) {
                onComplete(streamingResult);
              }
            } else {
              setCurrentNarration('⚠️ Synthesis stream timed out. Waiting for completion...');
            }
          }, 15000); // 15 second timeout
          
          // Create a function to clear the timeout when we receive data
          const clearStreamTimeout = () => {
            if (streamTimeout) {
              clearTimeout(streamTimeout);
              streamTimeout = null;
            }
          };
          
          // Add a handler to clear the timeout when we receive content
          const originalOnMessage = eventSource.onmessage;
          eventSource.onmessage = (event) => {
            // Clear the timeout when we receive any message
            clearStreamTimeout();
            // Call the original handler
            if (originalOnMessage) originalOnMessage(event);
          };
          
          // Clear the timeout when component unmounts or stream completes
          return () => {
            if (streamTimeout) clearTimeout(streamTimeout);
          };
        } else {
          console.warn('[AITeamOrchestrator] No directStreamUrl provided in synthesis_started event');
        }
        break;

      case 'synthesis_progress':
        setCurrentNarration(event.data.commentary || '🎨 Weaving outputs together...');
        break;

      case 'synthesis_streaming':
        setCurrentNarration(event.data.commentary || '🎨 Streaming response...');
        // Update the streaming result with the partial content
        if (event.data.partialResult) {
          setStreamingResult(event.data.partialResult);
        }
        break;

      case 'orchestration_completed':
        setCurrentNarration(event.data.commentary || '🎉 AI team collaboration complete!');
        setOrchestrationPhase('complete');
        setIsComplete(true);
        setStreamingResult(''); // Clear streaming result

        // If we have a streaming result, use that as the final result
        // Otherwise, use the finalResult from the event
        const result = streamingResult || event.data.finalResult;
        
        if (result) {
          setFinalResult(result);
          if (onComplete) {
            onComplete(result);
          }
        } else if (streamingResult) {
          // If we have a streaming result but no final result, use the streaming result
          setFinalResult(streamingResult);
          if (onComplete) {
            onComplete(streamingResult);
          }
        }
        
        // Close the direct stream if it's still open
        if (directStreamSource) {
          console.log('[AITeamOrchestrator] Closing direct stream on orchestration completion');
          directStreamSource.close();
          setDirectStreamSource(null);
        }
        break;

      case 'moderator_commentary':
        setCurrentNarration(event.data.commentary || '🤖 Moderator is coordinating...');
        break;
    }

    // Update overall progress
    const completedModels = modelStatuses.filter(m => m.status === 'completed').length;
    const totalModels = modelStatuses.length;
    if (totalModels > 0) {
      setOverallProgress((completedModels / totalModels) * 100);
    }
  }, [lastEvent, modelStatuses.length, onComplete, onError]);

  // Handle stream errors
  useEffect(() => {
    if (streamError && onError) {
      onError(streamError);
    }
  }, [streamError, onError]);
  
  // Cleanup direct stream source on unmount
  useEffect(() => {
    return () => {
      if (directStreamSource) {
        directStreamSource.close();
      }
    };
  }, [directStreamSource]);

  const getPhaseIcon = () => {
    switch (orchestrationPhase) {
      case 'planning':
        return <ClockIcon className="w-6 h-6 text-blue-500" />;
      case 'executing':
        return <CpuChipIcon className="w-6 h-6 text-orange-500 animate-pulse" />;
      case 'synthesizing':
        return <SparklesIcon className="w-6 h-6 text-purple-500 animate-spin" />;
      case 'complete':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      default:
        return <ClockIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getPhaseLabel = () => {
    switch (orchestrationPhase) {
      case 'planning':
        return 'Planning & Assignment';
      case 'executing':
        return 'AI Team Execution';
      case 'synthesizing':
        return 'Synthesizing Results';
      case 'complete':
        return 'Complete';
      default:
        return 'Initializing';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {getPhaseIcon()}
          <div>
            <h2 className="text-xl font-bold text-gray-900">AI Team Orchestration</h2>
            <p className="text-sm text-gray-600">{getPhaseLabel()}</p>
          </div>
        </div>
        
        {/* Connection Status */}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-gray-600">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* Overall Progress */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">Overall Progress</span>
          <span className="text-sm text-gray-600">{Math.round(overallProgress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${overallProgress}%` }}
          />
        </div>
      </div>

      {/* Live Narration */}
      <OrchestrationNarrator 
        currentNarration={currentNarration}
        phase={orchestrationPhase}
      />

      {/* Model Status Cards */}
      {modelStatuses.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Specialists</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {modelStatuses.map((model, index) => (
              <ModelStatusCard
                key={`${model.roleId}-${index}`}
                roleId={model.roleId}
                modelName={model.modelName}
                status={model.status}
                progress={model.progress}
                output={model.output}
                duration={model.duration}
                quality={model.quality}
              />
            ))}
          </div>
        </div>
      )}

      {/* Progress Visualization */}
      <OrchestrationProgress 
        modelStatuses={modelStatuses}
        currentPhase={orchestrationPhase}
        overallProgress={overallProgress}
      />

      {/* Final Result - Show either streaming result or final result */}
      {(streamingResult || (isComplete && finalResult)) && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-900 mb-2">
            {isComplete ? '🎉 Orchestration Complete!' : '🧩 Synthesizing Response...'}
          </h3>
          <div className="text-sm text-green-800 whitespace-pre-wrap">
            {streamingResult || finalResult}
          </div>
        </div>
      )}

      {/* Error State */}
      {streamError && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
            <h3 className="text-lg font-semibold text-red-900">Connection Error</h3>
          </div>
          <p className="text-sm text-red-800 mt-1">{streamError}</p>
        </div>
      )}
    </div>
  );
};
