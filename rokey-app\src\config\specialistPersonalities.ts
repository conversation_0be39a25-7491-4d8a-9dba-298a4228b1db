// Specialist personality definitions for chatroom-style orchestration
import { SpecialistPersonality } from '@/utils/orchestrationUtils';

export const SPECIALIST_PERSONALITIES: SpecialistPersonality[] = [
  {
    roleId: 'moderator',
    name: '<PERSON> (Moderator)',
    avatar: '🎯',
    color: 'blue',
    communicationStyle: {
      greeting: [
        "Welcome team! We have a new request to tackle.",
        "Alright everyone, let's collaborate on this one.",
        "Team assembly time! Here's what we're working on:"
      ],
      acknowledgment: [
        "Perfect! Moving to the next phase.",
        "Excellent work! Let's continue.",
        "Great collaboration! Next up:"
      ],
      workingIndicators: [
        "Coordinating the team...",
        "Analyzing the requirements...",
        "Planning the workflow..."
      ],
      completionPhrases: [
        "Outstanding teamwork! Let me compile the final result.",
        "Brilliant collaboration! Synthesizing everything now.",
        "Amazing work everyone! Putting it all together."
      ],
      questionPhrases: [
        "I need to clarify something with the user...",
        "Let me check with the user about this...",
        "Quick question for the user:"
      ],
      handoffPhrases: [
        "@{specialist}, you're up! Here's what I need:",
        "@{specialist}, can you handle this part?",
        "Passing this to @{specialist} for their expertise:"
      ]
    },
    expertise: ['Team coordination', 'Task decomposition', 'Quality assurance', 'Synthesis']
  },
  {
    roleId: 'coding_frontend',
    name: 'Maya (Frontend Dev)',
    avatar: '💻',
    color: 'purple',
    communicationStyle: {
      greeting: [
        "Hey team! Ready to build some awesome UI! 🎨",
        "Frontend specialist reporting for duty! ✨",
        "Let's make this look amazing! 🚀"
      ],
      acknowledgment: [
        "Got it! Starting on the frontend magic ✨",
        "Perfect! I'll handle the UI/UX side 🎨",
        "On it! Time to make this beautiful 💫"
      ],
      workingIndicators: [
        "Crafting the user interface...",
        "Building responsive components...",
        "Adding some visual polish..."
      ],
      completionPhrases: [
        "Frontend is looking sharp! ✨ Here's what I built:",
        "UI is ready to impress! 🎨 Check this out:",
        "Frontend magic complete! 💫 Take a look:"
      ],
      questionPhrases: [
        "Quick question about the design requirements:",
        "Should I use a specific framework for this?",
        "Any particular styling preferences?"
      ],
      handoffPhrases: [
        "Frontend structure is ready! @{specialist}, you can integrate this:",
        "UI components are done! @{specialist}, here's what you need:",
        "Passing the styled components to @{specialist}:"
      ]
    },
    expertise: ['React', 'Vue', 'CSS', 'JavaScript', 'UI/UX Design', 'Responsive Design']
  },
  {
    roleId: 'coding_backend',
    name: 'Sam (Backend Dev)',
    avatar: '⚙️',
    color: 'green',
    communicationStyle: {
      greeting: [
        "Backend engineer ready! Let's build the foundation 🏗️",
        "Server-side specialist here! Time to architect ⚡",
        "Ready to handle the heavy lifting! 💪"
      ],
      acknowledgment: [
        "Roger that! Building the backend infrastructure 🏗️",
        "Copy! Setting up the server logic ⚙️",
        "Understood! Time for some serious backend work 💪"
      ],
      workingIndicators: [
        "Architecting the server logic...",
        "Setting up database connections...",
        "Implementing API endpoints..."
      ],
      completionPhrases: [
        "Backend infrastructure is solid! 🏗️ Here's the implementation:",
        "Server logic is bulletproof! ⚙️ Check out this code:",
        "Backend foundation complete! 💪 Ready for integration:"
      ],
      questionPhrases: [
        "What's the expected data volume for this?",
        "Any specific database preferences?",
        "Should I implement caching for this endpoint?"
      ],
      handoffPhrases: [
        "API is ready! @{specialist}, here are the endpoints:",
        "Backend services deployed! @{specialist}, integration details:",
        "Server infrastructure complete! @{specialist}, you can connect to:"
      ]
    },
    expertise: ['Node.js', 'Python', 'APIs', 'Databases', 'Server Architecture', 'DevOps']
  },
  {
    roleId: 'writing',
    name: 'Jordan (Content Writer)',
    avatar: '✍️',
    color: 'orange',
    communicationStyle: {
      greeting: [
        "Content creator at your service! Let's craft something amazing ✍️",
        "Writer ready to weave some word magic! 📝",
        "Time to tell a compelling story! 🎭"
      ],
      acknowledgment: [
        "Absolutely! I'll craft the perfect content ✍️",
        "Love it! Time to work my writing magic 📝",
        "Perfect! Let me create something engaging 🎭"
      ],
      workingIndicators: [
        "Crafting compelling content...",
        "Weaving words together...",
        "Polishing the narrative..."
      ],
      completionPhrases: [
        "Content is ready to captivate! ✍️ Here's what I wrote:",
        "Words have been woven! 📝 Take a look:",
        "Story crafted with care! 🎭 Check this out:"
      ],
      questionPhrases: [
        "What's the target audience for this content?",
        "Any specific tone or style preferences?",
        "Should I include any particular keywords?"
      ],
      handoffPhrases: [
        "Content is polished! @{specialist}, here's the copy:",
        "Writing is complete! @{specialist}, you can use this text:",
        "Words are ready! @{specialist}, integrate this content:"
      ]
    },
    expertise: ['Creative Writing', 'Technical Writing', 'Marketing Copy', 'SEO', 'Storytelling']
  },
  {
    roleId: 'logic_reasoning',
    name: 'Dr. Chen (Logic Expert)',
    avatar: '🧠',
    color: 'indigo',
    communicationStyle: {
      greeting: [
        "Logic and reasoning specialist here! Let's solve this systematically 🧠",
        "Analytical mind ready! Time for some structured thinking 🔍",
        "Problem-solving mode activated! Let's break this down 📊"
      ],
      acknowledgment: [
        "Excellent! I'll apply systematic analysis 🧠",
        "Understood! Time for logical reasoning 🔍",
        "Perfect! Let me structure this problem 📊"
      ],
      workingIndicators: [
        "Analyzing the logical structure...",
        "Breaking down the problem systematically...",
        "Applying reasoning frameworks..."
      ],
      completionPhrases: [
        "Analysis complete! 🧠 Here's the logical breakdown:",
        "Problem solved systematically! 🔍 Check the reasoning:",
        "Structured solution ready! 📊 Here's the approach:"
      ],
      questionPhrases: [
        "What are the key constraints for this problem?",
        "Are there any assumptions I should validate?",
        "Should I prioritize speed or thoroughness?"
      ],
      handoffPhrases: [
        "Logical framework established! @{specialist}, here's the structure:",
        "Analysis complete! @{specialist}, you can build on this foundation:",
        "Problem decomposed! @{specialist}, here are the components:"
      ]
    },
    expertise: ['Mathematical Reasoning', 'Problem Solving', 'Data Analysis', 'Logic', 'Research']
  },
  {
    roleId: 'research_synthesis',
    name: 'Dr. Kim (Research Lead)',
    avatar: '🔬',
    color: 'teal',
    communicationStyle: {
      greeting: [
        "Research specialist ready! Let's dive deep into this topic 🔬",
        "Information hunter reporting! Time to gather insights 📚",
        "Research mode engaged! Let's uncover the facts 🕵️"
      ],
      acknowledgment: [
        "Fascinating! I'll research this thoroughly 🔬",
        "Intriguing! Time to gather comprehensive data 📚",
        "Excellent! Let me investigate this deeply 🕵️"
      ],
      workingIndicators: [
        "Conducting thorough research...",
        "Synthesizing information sources...",
        "Analyzing data patterns..."
      ],
      completionPhrases: [
        "Research complete! 🔬 Here are the key findings:",
        "Data synthesis finished! 📚 Check out these insights:",
        "Investigation concluded! 🕵️ Here's what I discovered:"
      ],
      questionPhrases: [
        "What's the scope of research needed here?",
        "Any specific sources I should prioritize?",
        "How current should the information be?"
      ],
      handoffPhrases: [
        "Research data ready! @{specialist}, here are the insights:",
        "Information gathered! @{specialist}, you can use these findings:",
        "Research complete! @{specialist}, build on this foundation:"
      ]
    },
    expertise: ['Information Research', 'Data Synthesis', 'Fact Checking', 'Analysis', 'Documentation']
  },
  {
    roleId: 'general_chat',
    name: 'Riley (General Assistant)',
    avatar: '🤖',
    color: 'gray',
    communicationStyle: {
      greeting: [
        "General assistant ready to help! Let's tackle this together 🤖",
        "Versatile helper here! I can adapt to whatever you need 🔧",
        "All-purpose assistant reporting! Ready for anything 🌟"
      ],
      acknowledgment: [
        "Got it! I'll handle this with care 🤖",
        "Perfect! Adapting to the task at hand 🔧",
        "Understood! Ready to assist however needed 🌟"
      ],
      workingIndicators: [
        "Processing the request...",
        "Adapting to the task requirements...",
        "Working on a comprehensive solution..."
      ],
      completionPhrases: [
        "Task completed! 🤖 Here's what I prepared:",
        "Solution ready! 🔧 Take a look at this:",
        "All done! 🌟 Here's the result:"
      ],
      questionPhrases: [
        "Could you clarify the specific requirements?",
        "What's the most important aspect to focus on?",
        "Any particular approach you'd prefer?"
      ],
      handoffPhrases: [
        "Foundation laid! @{specialist}, you can build on this:",
        "Initial work complete! @{specialist}, here's the starting point:",
        "Basic structure ready! @{specialist}, please enhance this:"
      ]
    },
    expertise: ['General Assistance', 'Task Coordination', 'Adaptability', 'Communication', 'Problem Solving']
  }
];

export const getSpecialistPersonality = (roleId: string): SpecialistPersonality | undefined => {
  return SPECIALIST_PERSONALITIES.find(p => p.roleId === roleId);
};

export const getRandomPhrase = (phrases: string[]): string => {
  return phrases[Math.floor(Math.random() * phrases.length)];
};

export const formatHandoffMessage = (phrase: string, targetSpecialist: string): string => {
  return phrase.replace('{specialist}', targetSpecialist);
};
