// Real-time orchestration streaming endpoint for live AI team collaboration

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { OrchestrationEvent, createOrchestrationEventStream } from '@/utils/orchestrationUtils';

// Global map to track active streams
const activeStreams = new Map<string, {
  controller: ReadableStreamDefaultController;
  lastEventId: string;
  startTime: number;
}>();

export async function GET(
  request: NextRequest,
  { params }: { params: { executionId: string } }
) {
  const { executionId } = params;
  
  if (!executionId) {
    return NextResponse.json(
      { error: 'Execution ID is required' },
      { status: 400 }
    );
  }

  const supabase = createSupabaseServerClientOnRequest();
  
  try {
    // Verify execution exists and user has access
    const { data: execution, error } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (error || !execution) {
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }

    // Create Server-Sent Events stream
    const encoder = new TextEncoder();
    let streamController: ReadableStreamDefaultController;

    const stream = new ReadableStream({
      start(controller) {
        streamController = controller;
        
        // Store stream reference for broadcasting events
        activeStreams.set(executionId, {
          controller,
          lastEventId: '',
          startTime: Date.now()
        });

        // Send initial connection event
        const connectionEvent = {
          id: crypto.randomUUID(),
          execution_id: executionId,
          type: 'stream_connected',
          timestamp: new Date().toISOString(),
          data: {
            message: '🎬 Connected to AI team orchestration stream',
            execution: {
              id: execution.id,
              status: execution.status,
              total_steps: execution.total_steps,
              created_at: execution.created_at
            }
          }
        };

        const eventData = formatSSEEvent(connectionEvent);
        controller.enqueue(encoder.encode(eventData));

        // Send historical events if execution is in progress or completed
        if (execution.status !== 'pending') {
          sendHistoricalEvents(executionId, controller, encoder);
        }

        console.log(`[Orchestration Stream] Client connected to execution ${executionId}`);
      },

      cancel() {
        // Clean up stream reference
        activeStreams.delete(executionId);
        console.log(`[Orchestration Stream] Client disconnected from execution ${executionId}`);
      }
    });

    // Return SSE response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });

  } catch (error) {
    console.error(`[Orchestration Stream] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Send historical events for ongoing or completed executions
async function sendHistoricalEvents(
  executionId: string,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
) {
  const supabase = createSupabaseServerClientOnRequest();
  
  try {
    // Get orchestration steps with their current status
    const { data: steps, error } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', executionId)
      .order('step_number', { ascending: true });

    if (error) {
      console.error(`[Orchestration Stream] Error fetching steps: ${error}`);
      return;
    }

    // Send events for each step based on its status
    for (const step of steps || []) {
      const events = generateHistoricalEventsForStep(step, executionId);
      
      for (const event of events) {
        const eventData = formatSSEEvent(event);
        controller.enqueue(encoder.encode(eventData));
        
        // Small delay between events for better UX
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

  } catch (error) {
    console.error(`[Orchestration Stream] Error sending historical events: ${error}`);
  }
}

// Generate historical events based on step status
function generateHistoricalEventsForStep(step: any, executionId: string): OrchestrationEvent[] {
  const events: OrchestrationEvent[] = [];
  const baseEvent = {
    execution_id: executionId,
    step_number: step.step_number,
    role_id: step.role_id,
    model_name: step.model_name
  };

  // Step assigned event
  events.push({
    id: crypto.randomUUID(),
    ...baseEvent,
    type: 'step_assigned',
    timestamp: step.created_at,
    data: {
      commentary: `📋 ${step.role_id} specialist assigned to step ${step.step_number}`,
      step: {
        number: step.step_number,
        role: step.role_id,
        model: step.model_name,
        prompt: step.prompt.substring(0, 100) + '...'
      }
    }
  });

  // Step started event (if started)
  if (step.started_at) {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_started',
      timestamp: step.started_at,
      data: {
        commentary: `🚀 ${step.role_id} is now working on this challenge...`,
        estimatedDuration: step.duration_ms || 45000
      }
    });
  }

  // Step progress events (if in progress)
  if (step.status === 'in_progress') {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_progress',
      timestamp: new Date().toISOString(),
      data: {
        commentary: `⚡ ${step.role_id} is making excellent progress...`,
        progress: 0.6,
        partialOutput: step.response ? step.response.substring(0, 200) + '...' : null
      }
    });
  }

  // Step completed event (if completed)
  if (step.status === 'completed' && step.completed_at) {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_completed',
      timestamp: step.completed_at,
      data: {
        commentary: `✅ Outstanding work from ${step.role_id}! Moving to next phase.`,
        output: step.response,
        duration: step.duration_ms,
        tokens: {
          input: step.tokens_in,
          output: step.tokens_out
        },
        cost: step.cost,
        quality: 0.9 // Could be calculated based on validation
      }
    });
  }

  // Step failed event (if failed)
  if (step.status === 'failed') {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_failed',
      timestamp: step.completed_at || new Date().toISOString(),
      data: {
        commentary: `❌ ${step.role_id} encountered an issue. Analyzing options...`,
        error: step.error_message,
        retryPlan: 'Attempting automatic recovery'
      }
    });
  }

  return events;
}

// Format event for Server-Sent Events
function formatSSEEvent(event: OrchestrationEvent): string {
  return `id: ${event.id}\nevent: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
}

// Broadcast event to all connected clients for an execution
export function broadcastOrchestrationEvent(
  executionId: string,
  event: OrchestrationEvent
): void {
  const streamInfo = activeStreams.get(executionId);
  
  if (streamInfo) {
    try {
      const encoder = new TextEncoder();
      const eventData = formatSSEEvent(event);
      streamInfo.controller.enqueue(encoder.encode(eventData));
      streamInfo.lastEventId = event.id;
      
      console.log(`[Orchestration Stream] Broadcasted ${event.type} to execution ${executionId}`);
    } catch (error) {
      console.error(`[Orchestration Stream] Error broadcasting event: ${error}`);
      // Remove dead stream
      activeStreams.delete(executionId);
    }
  }
}

// Get active stream count for monitoring
export function getActiveStreamCount(): number {
  return activeStreams.size;
}

// Clean up old streams (called periodically)
export function cleanupOldStreams(maxAgeMs: number = 30 * 60 * 1000): void {
  const now = Date.now();
  
  for (const [executionId, streamInfo] of activeStreams.entries()) {
    if (now - streamInfo.startTime > maxAgeMs) {
      try {
        streamInfo.controller.close();
      } catch (error) {
        console.warn(`[Orchestration Stream] Error closing old stream: ${error}`);
      }
      activeStreams.delete(executionId);
      console.log(`[Orchestration Stream] Cleaned up old stream for execution ${executionId}`);
    }
  }
}
