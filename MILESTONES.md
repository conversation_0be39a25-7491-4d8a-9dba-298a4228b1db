# RoKey Project Milestones (Updated: 2025-06-03)

## Overview
RoKey is a Next.js application with a Supabase backend designed to manage and route LLM API requests effectively. It aims to provide robust key management, flexible routing strategies, logging, and analytics for LLM API usage.

## Completed Milestones

### Milestone 1: Base System Setup (Complete)
*   Supabase project setup and table schema definition (`api_keys`, `routing_rules`, `models`, `request_logs`).
*   Initialize Next.js project (`rokey-app`) with TypeScript, Tailwind CSS, App Router, and `src` directory.
*   Setup Supabase client configuration (`lib/supabase/client.ts`, `lib/supabase/server.ts`).
*   Basic frontend layout (`Navbar.tsx`, `Sidebar.tsx`, `app/layout.tsx`).
*   Placeholder pages for main sections with basic navigation.
*   Environment variable setup for Supabase credentials (`.env.local`).
*   **Status:** COMPLETE

### Milestone 2: Custom API Configurations & Key Management (Complete)
*   **Database Schema Update:**
    *   Create `custom_api_configs` table (id UUID PK, user_id UUID FK to auth.users, name TEXT, created_at TIMESTAMPTZ, updated_at TIMESTAMPTZ).
    *   Modify `api_keys` table: add `custom_api_config_id` (UUID FK to `custom_api_configs.id`), rename existing `model_id` to `predefined_model_id` (TEXT FK to `models.id`).
*   **Backend for Custom API Configs:**
    *   `POST /api/custom-configs`: Create a new configuration (requires `name`).
    *   `GET /api/custom-configs`: List user's configurations.
    *   `DELETE /api/custom-configs/:id`: Delete a configuration (cascade deletion of associated keys and role assignments handled by DB).
*   **Backend for API Keys (within a Configuration):**
    *   `POST /api/keys`: Requires `custom_api_config_id`, `provider`, `predefined_model_id`, `api_key_raw`, `label`. Auto-defaults first key if no other default exists.
    *   `GET /api/keys?custom_config_id=<ID>`: List keys for a specific configuration.
    *   `DELETE /api/keys/:keyId`: Delete an individual API key.
*   **Frontend UI - "My Models" Page (`/my-models`):**
    *   Sidebar navigation link to this page.
    *   List all custom API configurations with options to view details or delete.
    *   Interface to create new custom API configurations.
*   **Frontend UI - Individual Configuration Detail Page (`/my-models/[configId]`):**
    *   Display selected custom API configuration name.
    *   Form to add new API keys (Provider, Predefined Model, API Key Value, Label) to the current configuration. `llmProviders` list in `config/models.ts` filtered to show only directly supported providers.
    *   Table listing API keys for this configuration, showing Label, Provider, Predefined Model, Status, Assigned Roles, Default Status, and actions (Manage Roles, Set Default, Delete).
*   **Core Functionality:**
    *   API key encryption (AES-256-GCM using `ROKEY_ENCRYPTION_KEY`) for `encrypted_api_key`.
    *   `api_key_hash` (SHA-256 of raw key) with a composite unique constraint `unique_key_hash_per_config (custom_api_config_id, api_key_hash)` on `api_keys` table.
    *   `models` table populated from `config/models.ts` for provider/model dropdowns.
*   **Status:** COMPLETE

### Milestone 3: Role-Based Routing Setup for API Keys (Complete)
*   **Depends on:** Milestone 2.
*   **Concept:** Within each "Custom API Configuration", users assign specific operational roles (e.g., `Logic`, `Copywriting`) to individual API keys. One key per configuration is designated as the "Default General Chat" model.
*   **Predefined Roles:** System-wide list in `config/roles.ts` (e.g., `id`, `name`, `description`).
*   **Database Schema Changes:**
    *   **New Table: `api_key_role_assignments`**:
        *   `id` (UUID PK), `api_key_id` (FK), `custom_api_config_id` (FK), `role_name` (TEXT).
        *   Unique Constraints: `(api_key_id, role_name)` and `(custom_api_config_id, role_name)`.
        *   `created_at`, `updated_at` TIMESTAMPTZ.
    *   **Modify `api_keys` Table:**
        *   Add `is_default_general_chat_model` (BOOLEAN, default false).
        *   Partial Unique Index: `unique_default_general_chat_model_per_config` on `api_keys (custom_api_config_id) WHERE is_default_general_chat_model = TRUE`.
*   **Backend API Routes:**
    *   Role Assignments:
        *   `GET /api/keys/:apiKeyId/roles`: List roles for a key.
        *   `POST /api/keys/:apiKeyId/roles`: Assign a role (body: `{ role_name: string }`).
        *   `DELETE /api/keys/:apiKeyId/roles/:roleName`: Unassign a role.
    *   Default General Chat Model:
        *   `PUT /api/custom-configs/:configId/default-key-handler/:apiKeyId`: Set a key as default (unsetting others in the same config).
        *   `GET /api/custom-configs/:configId/default-chat-key`: Get the current default key for a config.
*   **Frontend UI (on `/my-models/[configId]` page):**
    *   Display assigned roles as badges/tags next to each API key.
    *   Button/modal to manage roles for each key (checkboxes for assignable roles).
    *   Button to set/unset a key as "Default General Chat Model".
    *   Visual indicators for default status (e.g., badge, icon).
    *   Optimistic UI updates for role and default key changes.
    *   User-friendly error messages for constraint violations (e.g., role already assigned).
*   **Documentation Clarification:**
    *   Updated placeholder page `/routing-setup` to explain its future purpose for advanced routing (load balancing, failover) and to direct users to individual Custom Model pages for current role/default setup.
*   **Status:** COMPLETE

### Milestone 3.5: Dynamic Model Fetching for API Key Configuration (Complete)
*   **Final State:** This milestone significantly evolved. Instead of dynamic, per-key model fetching from individual providers at runtime for UI population, RoKey now relies on a centralized, admin-curated list of models.
    *   **Admin-Populated `public.models` Table:** The `public.models` table in Supabase serves as the single source of truth for all LLM models RoKey is aware of. This table is populated by an administrator running the `supabaseWriter.py` script, which fetches comprehensive model data (including rich details like family, modality, context windows, versions, etc.) from OpenRouter.
    *   **`supabaseWriter.py`:** This Python script is crucial for maintaining the model list. It connects to Supabase, deletes existing models, fetches the latest from OpenRouter, transforms the data (maps provider slugs via `PROVIDER_CONFIG`), and inserts the full list. Any updates to available models in RoKey require re-running this script.
    *   **Schema for `public.models`:** The table schema (`comprehensive_schema_correction.sql`) was updated to include columns like `display_name`, `provider_id` (internal RoKey provider slug), `family`, `modality`, `context_window`, `input_token_limit`, `output_token_limit`, `version`, `provider_specific_details` (JSONB for raw OpenRouter data like architecture, pricing), and `model_metadata` (JSONB for source info). The SQL script now *only* defines schema and does not insert static model data.
    *   **Simplified API (`/api/providers/list-models`):** This Next.js API route now directly queries the `public.models` table in Supabase to return the complete list of models to the frontend. It no longer makes calls to individual LLM providers.
    *   **Frontend (`/my-models/[configId]`):**
        *   The "Specific Model Variant" dropdown is populated from the data fetched by `/api/providers/list-models`.
        *   When a specific "LLM Provider" (e.g., "OpenAI") is selected, the model dropdown filters based on the `provider_id` in the fetched model data.
        *   When "OpenRouter" is selected as the "LLM Provider", the model dropdown lists **all** models from the database, as an OpenRouter key can access any of them.
    *   **API Key Saving with "OpenRouter":** When saving a key with "OpenRouter" as the provider, the `api_keys.provider` is "OpenRouter", and `api_keys.predefined_model_id` is the OpenRouter-specific model ID (e.g., `google/gemini-flash-1.5`).
    *   **Database Constraint:** The `api_keys_provider_check` constraint in `comprehensive_schema_correction.sql` was updated to include "OpenRouter" as a valid provider name in the `api_keys` table.
*   **Key Takeaways & Considerations for Milestone 4 (Unified API Endpoint):**
    *   **Model Information Source:** The unified endpoint will rely on the `provider` and `predefined_model_id` stored in the `api_keys` table.
    *   **"OpenRouter" Key Handling:** If the `api_keys.provider` is "OpenRouter", the routing logic must know to:
        *   Use the decrypted OpenRouter API key.
        *   Send the request to the OpenRouter API endpoint (`https://openrouter.ai/api/v1/chat/completions`).
        *   Use the `api_keys.predefined_model_id` (which will be the OpenRouter model string, e.g., `openai/gpt-4o`) as the `model` parameter in the request to OpenRouter.
    *   **Direct Provider Key Handling:** If `api_keys.provider` is a direct provider (e.g., "openai", "google"), the logic will:
        *   Use the decrypted API key for that specific provider.
        *   Determine the correct base URL for that provider (potentially from `config/models.ts` or an enriched `public.models` entry).
        *   Use `api_keys.predefined_model_id` as the model identifier for that direct provider.
    *   **Database as Source of Truth:** The system's knowledge of available models and their properties for routing decisions should stem from what's in `public.models`. The unified endpoint might need to query this table to get further model details (like specific API path if not using OpenRouter, or other capabilities) based on the `predefined_model_id`.
    *   **No Dynamic Model List Fetching by Unified Endpoint:** The unified API endpoint itself does *not* need to (and should not) fetch model lists from providers. That is now an admin-time task via `supabaseWriter.py`.
*   **Status:** COMPLETE

### Milestone 4: Unified API Endpoint with Role-Based Routing & Playground (Complete)
*   **Objective:**
    *   Create the primary backend endpoint (`/api/v1/chat/completions`) for LLM requests, incorporating role-based routing and multimodal support.
    *   Develop a frontend "Playground" (`/playground`) for testing Custom API Configurations with text and image inputs.
*   **Key Accomplishments & Features:**
    *   **Backend - Unified API Endpoint (`/api/v1/chat/completions/route.ts`):**
        *   **Core Functionality:** Implemented robust API token authentication (using `ROKEY_API_ACCESS_TOKEN`), Zod-based request body validation (supporting string or array content for messages), and CORS `OPTIONS` handler.
        *   **Routing & Key Management:**
            *   `getApiKeyForRouting` helper successfully retrieves API keys based on `custom_api_config_id` and optional `role`, falling back to `is_default_general_chat_model`.
            *   API keys are decrypted using `ROKEY_ENCRYPTION_KEY`.
            *   `getDirectProviderModelId` helper correctly strips prefixes (e.g., `openai/`) for direct provider API calls.
        *   **Provider Integration & Proxying:**
            *   Successfully proxied requests and handled responses/errors for: OpenAI, OpenRouter, Google Gemini, Anthropic Claude, DeepSeek, and XAI (Grok).
            *   Provider-specific payload transformations and header requirements (e.g., Anthropic headers, Google API structure) were implemented.
        *   **Google Gemini Special Handling:**
            *   Adapted to map full message history (user/assistant roles, system prompts to `system_instruction`).
            *   Implemented robust Server-Sent Events (SSE) transformation for streaming, correctly handling Google's tendency to send an array of JSON objects in a single stream chunk by parsing the array and generating individual SSE `data:` events.
            *   Multimodal input support: Parses `image_url` (base64 data URLs) from the request and converts them to Gemini's `inline_data` format (`mime_type`, `data`).
        *   **API Key Logic:**
            *   Corrected API key uniqueness to be per configuration (`CONSTRAINT unique_key_hash_per_config UNIQUE (custom_api_config_id, api_key_hash)` on `api_keys`).
            *   Implemented logic in `POST /api/keys` to auto-set the first added API key as `is_default_general_chat_model = true` if no other default exists for the configuration.
    *   **Frontend - Playground (`/app/playground/page.tsx`):**
        *   **UI & Core Functionality:**
            *   Page created and linked from the main `Sidebar.tsx`.
            *   Dropdown for selecting user's Custom API Configurations.
            *   Toggle for enabling/disabling streaming.
        *   **Multimodal Input (Images):**
            *   File input (`<input type="file" accept="image/*">`) styled with `PaperClipIcon` for image selection.
            *   Image preview displayed below the message input with a remove button (`XCircleIcon`).
            *   `fileToBase64` helper function for converting image files.
            *   `PlaygroundMessage` interface updated to use `content: PlaygroundMessageContentPart[]`, where parts can be `{type: 'text', text: '...'}` or `{type: 'image_url', image_url: {url: 'data:...'}}`.
            *   `handleSendMessage` constructs payloads compatible with OpenAI's multimodal message format (string `content` for text-only, array of parts for mixed content).
            *   Chat history correctly renders both text and image message parts.
        *   **SSE Handling:** Refined the client-side SSE processing loop to reliably terminate on `data: [DONE]` signals, even when multiple SSE events arrive rapidly from the backend.
    *   **Backend-for-Frontend (BFF - `/app/api/playground/route.ts`):**
        *   Created to securely proxy requests from the Playground to `/api/v1/chat/completions`, adding the `ROKEY_API_ACCESS_TOKEN` on the server-side.
        *   Handles streaming back to the client.
        *   Noted for future enhancement: Add user authentication to this BFF route.
    *   **Configuration & Database:**
        *   Updated `llmProviders` in `rokey-app/src/config/models.ts` to include only the 6 directly supported providers for the "Add API Key" UI dropdown.
        *   Addressed various Supabase `request_logs` schema discrepancies by adding missing columns (e.g., `error_source`, `llm_model_name`, `llm_provider_latency_ms`, `llm_provider_name`).
*   **Testing:** Conducted extensive testing via PowerShell, Postman, and the Playground UI to confirm authentication, Zod validation, CORS, routing logic, provider integration (OpenAI, OpenRouter, Google, Anthropic, DeepSeek, XAI), streaming (especially for Gemini), and multimodal image functionality.
*   **Status:** COMPLETE

### Milestone 5: Request & Response Logging (Complete)
*   **Depends on:** Milestone 4.
*   **Objective:** Enhance the logging capabilities of the unified API endpoint to capture detailed information for analytics, debugging, and potential cost tracking.
*   **Backend - Enhance `request_logs` Table & Logic:**
    *   **Review & Refine `request_logs` Columns:** Ensure all necessary columns are present and correctly typed based on data logged at the end of M4. This includes:
        *   `custom_api_config_id`, `api_key_id`, `predefined_model_id`
        *   `role_requested`, `role_used`
        *   `ip_address` (from `request.ip`)
        *   `request_timestamp`, `response_timestamp`
        *   `status_code` (final HTTP status to client)
        *   `request_payload_summary` (JSONB: messages_count, model_requested_passthrough, streaming, temperature, max_tokens)
        *   `response_payload_summary` (JSONB: usage from LLM, finish_reason, has_error indication)
        *   `error_message` (TEXT)
        *   `error_source` (TEXT - 'RoKey', 'LLM Provider')
        *   `error_details_zod` (TEXT - stringified Zod issues)
        *   `llm_provider_name` (TEXT)
        *   `llm_model_name` (TEXT - `predefined_model_id` used)
        *   `llm_provider_status_code` (INTEGER - status from the actual LLM)
        *   `llm_provider_latency_ms` (INTEGER)
        *   `processing_duration_ms` (INTEGER)
        *   `user_id` (UUID, FK to `auth.users.id` - for M13).
        *   `cost` (NUMERIC - placeholder for M10, initially null).
        *   `tokens_prompt` (INTEGER - if parsable from request/response summary).
        *   `tokens_completion` (INTEGER - if parsable from request/response summary).
        *   Indication of multimodal content (e.g., boolean flag or count of image parts).
    *   **Update Unified API Endpoint Logging Logic:**
        *   Systematically populate all relevant fields in `request_logs` within the `finally` block of the `/api/v1/chat/completions` route.
        *   Ensure consistent error capture and logging for both RoKey internal errors and errors from LLM providers.
        *   Implement helper functions if needed to summarize request/response payloads safely (e.g., `summarizeLlmResponse` currently used).
*   **Considerations:**
    *   **Log Volume:** Strategies for managing log volume might be needed in the long term.
    *   **PII/Sensitive Data:** Continue to be mindful of not logging full sensitive prompt/completion text unless explicitly configured for debugging with appropriate safeguards.
*   **Status:** COMPLETE

### Milestone 6: Basic Logs Viewer (Complete)
*   **Depends on:** Milestone 5.
*   **Objective:** Provide a simple frontend interface for users to view their API request logs.
*   **Backend - BFF API Route (`/api/logs`):**
    *   Created to fetch `request_logs` data from Supabase.
    *   Supports pagination (page, pageSize).
    *   Supports filtering by date range (startDate, endDate), `customApiConfigId`, and status (success/error).
    *   Supports sorting by multiple columns (`request_timestamp`, `status_code`, `llm_provider_name`, `llm_model_name`, `llm_provider_latency_ms`, `processing_duration_ms`, `tokens_prompt`, `tokens_completion`, `custom_api_config_id`, `role_used`) and order (asc/desc).
*   **Frontend - "Logs" Page (`/app/logs/page.tsx`):**
    *   **Navigation:** Existing sidebar link to `/logs` utilized.
    *   **Data Fetching:** Fetches logs from the `/api/logs` BFF API.
    *   **Display:**
        *   Implemented a responsive table view for logs.
        *   Columns: Timestamp, Custom Model Name, Role Used, LLM Provider, LLM Model Name, Status (with color-coding), Latency (LLM), Latency (RoKey), Tokens (Prompt & Completion displayed separately), Multimodal flag, Actions.
    *   **Filtering UI:**
        *   Collapsible filter section.
        *   Date range pickers (start and end).
        *   Dropdown to filter by Custom API Configuration (name).
        *   Dropdown to filter by Status (All, Success, Error).
        *   "Apply Filters" and "Reset Filters" buttons.
    *   **Sorting UI:**
        *   Table headers for sortable columns are clickable to change sort field and toggle order (asc/desc).
        *   Visual indicators (arrows) on headers show current sort status.
    *   **Pagination UI:**
        *   "Previous" and "Next" buttons.
        *   Display of current page, total pages, and total items.
        *   Buttons are disabled appropriately (e.g., on first/last page, during loading).
    *   **Log Details View (Modal):**
        *   Clicking an "View Details" icon (eye icon) on a log row opens a modal.
        *   Modal displays all relevant log fields, including `request_payload_summary`, `response_payload_summary`, and `error_details_zod` (pretty-printed JSON).
*   **User Authentication Context (Future - M13):** Currently fetches all logs. Will be restricted by authenticated user in Milestone 13.
*   **Status:** COMPLETE

### Milestone 7: Advanced Routing Strategies (Complete)
*   **Description:** Implement sophisticated, configurable routing strategies to allow users precise control over how API keys are selected for requests, and establish a robust default behavior when no explicit strategy is chosen.
*   **Key Features Implemented & Verified:**
    *   **Intelligent Role Routing:** Successfully implemented LLM-based prompt classification to determine the most relevant operational role. Routes requests to API keys specifically assigned to the classified role. Includes fallback to the "Default General Chat Model".
    *   **Complexity-Based Round-Robin:** Successfully implemented LLM-based classification of prompt complexity (1-5). Routes requests by round-robin among active keys assigned to that complexity level, with proximal level searching if no exact match. Round-robin indices are persisted.
    *   **Strict Fallback (Ordered Failover):** Successfully implemented a strategy allowing users to define an ordered list of API keys for sequential failover attempts.
    *   **Default Behavior (No Explicit Strategy / "None"):** When no specific routing strategy is chosen, RoKey now defaults to intelligent load balancing (round-robin) across all active API keys for the configuration. This includes **true intra-request retry**: if a selected key fails with a retryable error, RoKey automatically attempts the same request with the next available key in the pool within the same incoming user request.
    *   **"Auto Optimal Model Routing" Refinement:** The concept of a separate "Auto Optimal" strategy was removed. Its core goals of resilience and smart default behavior were incorporated into the enhanced "None (Default Behavior)" strategy. Frontend UI was updated.
*   **Status:** COMPLETE

## Upcoming Milestones

### Milestone 8: UI Enhancements & Polish (Complete)
*   **Description:** Focus on comprehensive UI/UX polishing. Ensure the application is visually appealing with a beautiful and modern design, highly responsive, and provides a seamless user experience.
*   **Key Accomplishments:**
    *   **Premium Design System:** Implemented $10M app quality aesthetics with glass morphism effects, modern color palette, and sophisticated visual hierarchy.
    *   **Reference-Perfect Playground:** Complete redesign matching provided reference images with fixed header controls, spacious chat interface, and professional message bubbles.
    *   **Enhanced Log Message Transformation:** Created comprehensive system to transform debug-style technical patterns into user-friendly, readable text with intelligent fallback handling.
    *   **Token Usage Tracking Fixes:** Resolved null token values by updating Google's API response format handling and renamed database columns to industry-standard terminology (`input_tokens`/`output_tokens`).
    *   **Markdown Rendering:** Implemented beautiful formatted AI responses with syntax highlighting and proper typography integration.
    *   **Cross-Provider Token Extraction:** Enhanced token extraction logic for Google, Anthropic, and OpenAI-compatible providers with backward compatibility.
    *   **Visual Consistency:** Unified design language across all pages with consistent rounded corners, shadows, spacing, and color schemes.
    *   **Professional Data Display:** Clean, readable logs with meaningful routing information and accurate token tracking.
*   **Status:** COMPLETE

### Milestone 9: Training System & Knowledge Base Integration (Complete)
*   **Description:** Implement a comprehensive training system that allows users to upload documents and training prompts to enhance their AI models with domain-specific knowledge.
*   **Key Accomplishments:**
    *   **Document Upload System:** Full support for PDF, DOCX, TXT, and MD files with automatic content extraction
    *   **Knowledge Base Integration:** Documents are automatically processed and integrated into AI model responses
    *   **Training Prompts:** Support for structured training prompts with system instructions, behavior guidelines, and examples
    *   **Multi-Document Support:** Optimized for 6-7 substantial documents (2K words each) with 75K character limit
    *   **Smart Truncation:** Intelligent truncation that preserves complete document sections
    *   **Real-Time Updates:** Knowledge base automatically updates when files are added or removed
    *   **Debug Tools:** Comprehensive debugging and recovery tools for troubleshooting
    *   **File Management:** Robust frontend interface for managing uploaded documents
*   **Status:** COMPLETE

### Milestone 10: Cost Tracking & Basic Analytics (Complete)
*   **Description:** Implement accurate cost calculation for each request and provide basic analytics.
*   **Key Accomplishments:**
    *   **Database Schema:** Added `input_token_price` and `output_token_price` columns to `public.models` table
    *   **Pricing Data:** Populated comprehensive pricing data for all supported model variants from official sources
    *   **Backend Cost Calculation:** Implemented comprehensive cost calculation logic in `/api/v1/chat/completions/route.ts` with:
        *   OpenRouter direct cost tracking (credits to USD conversion)
        *   Google Gemini free tier detection via rate limit headers (≤60 RPM = free tier)
        *   DeepSeek free tier detection (always free)
        *   Manual pricing calculation for all direct providers (OpenAI, Anthropic, XAI)
        *   Cost field properly stored in `request_logs` table
    *   **Analytics API:** Created `/api/analytics/summary` endpoint with:
        *   Comprehensive aggregation of cost, token counts, and request counts
        *   Grouping by user/config, provider, model, and time periods
        *   Support for date filtering and multiple grouping options
    *   **Frontend Analytics:** Implemented analytics display in:
        *   Dashboard page with cost overview and key metrics
        *   Credits page with detailed cost breakdown by provider
        *   Real-time cost tracking in request logs with accurate costs for all providers
*   **Optional Future Enhancements:**
    *   Create dedicated Analytics Dashboard page (`/app/analytics`) with advanced charts and visualizations
    *   Add cost alerting and budget management features
*   **Status:** COMPLETE

### Milestone 11: User Management & Authentication UI (Refinement)
*   **Description:** Enhance user management features and refine the authentication UI flows.
*   **Status:** PENDING

### Milestone 12: Tool Integration & External Service Access
*   **Depends on:** Milestone 11 (User Management & Authentication UI).
*   **Description:** Enable users to grant their AI models access to external tools and services, allowing for enhanced automation and data integration capabilities.
*   **Key Features:**
    *   **Tool Configuration System:**
        *   Database schema for storing user tool configurations and credentials
        *   Secure credential storage with encryption for API keys and OAuth tokens
        *   Per-configuration tool access management (users can enable/disable tools for specific Custom API Configurations)
    *   **Supported Tool Integrations:**
        *   **Google Workspace:** Google Docs (read/write documents), Google Drive (file management, search), Google Sheets (data manipulation)
        *   **Supabase:** Direct database access for the user's own Supabase projects (query, insert, update data)
        *   **Web Browser:** Automated browsing, web scraping, research capabilities with headless browser integration
        *   **Notion:** Page creation, database queries, content management
        *   **GitHub:** Repository access, code analysis, issue management, pull request operations
        *   **YouTube:** Video information retrieval, transcript access, channel analytics
    *   **Backend Implementation:**
        *   Tool execution engine that safely handles external API calls
        *   Rate limiting and quota management for tool usage
        *   Audit logging for all tool interactions
        *   Sandboxed execution environment for browser automation
    *   **Frontend UI:**
        *   Tool configuration page (`/tools` or within `/my-models/[configId]/tools`)
        *   OAuth flow integration for services requiring authentication
        *   Tool usage analytics and monitoring dashboard
        *   Per-tool permission management interface
    *   **API Integration:**
        *   Extend `/api/v1/chat/completions` to support tool calls in LLM responses
        *   Function calling integration with OpenAI-compatible providers
        *   Tool result injection back into conversation context
    *   **Security & Compliance:**
        *   Secure credential storage with user-specific encryption keys
        *   Tool access scoping (read-only vs read-write permissions)
        *   Usage monitoring and anomaly detection
        *   Compliance with external service terms of service
*   **Status:** PENDING

### Milestone 13: User-Generated RoKey API Keys
*   **Depends on:** Milestone 11 (User Management & Authentication UI).
*   **Description:** Allow authenticated RoKey users to generate, manage, and revoke their own API keys. These keys will be used to authenticate requests made to the RoKey unified API endpoint (`/api/v1/chat/completions`) for their specific configurations, replacing or supplementing the global `ROKEY_API_ACCESS_TOKEN` for user-specific traffic.
*   **Tasks:**
    *   **Database:** Design schema for storing user-generated RoKey API keys (e.g., `user_rokey_api_keys` table, linked to `auth.users` and potentially `custom_api_configs`). Store hashed keys.
    *   **Backend:**
        *   Develop API endpoints for CRUD operations on user-generated RoKey API keys (create, list, revoke).
        *   Implement logic for API key generation (secure random strings, prefixes for identification).
        *   Update the authentication mechanism in `/api/v1/chat/completions` to validate these user-specific RoKey API keys.
        *   Integrate with logging to record which RoKey API key was used for a request.
    *   **Frontend:**
        *   Create a new UI section (e.g., "API Access" or within "Account Settings") for users to manage their RoKey API keys.
        *   Allow users to generate new keys, view existing key prefixes/names, copy keys (show once), and revoke keys.
    *   **Security:** Ensure secure storage (hashing), generation, and handling of these keys. Consider adding features like key expiry or usage scopes in the future.
*   **Status:** PENDING

### Milestone 14: Advanced API Key Options (Rate Limits, Quotas, Status)
*   **Description:** Allow users to set custom rate limits, spending quotas (per key, per config), and manually toggle key status (active/inactive) directly through the UI.
*   **Status:** PENDING

### Milestone 15: Caching Strategies for LLM Responses
*   **Description:** Implement optional caching for LLM responses to reduce latency and cost for identical or very similar requests.
*   **Status:** PENDING

### Milestone 16: Advanced Analytics & Reporting
*   **Description:** Expand the analytics dashboard with more detailed reports, visualizations, and potentially cost alerting.
*   **Status:** PENDING

### Milestone 17: Team/Organization Support (Basic)
*   **Description:** Introduce basic support for multiple users under a team or organization, with shared configurations and keys (admin-managed).
*   **Status:** PENDING

### Milestone 18: Admin Dashboard & System Monitoring
*   **Description:** Create an admin-level dashboard for system-wide monitoring, user management, and global settings.
*   **Status:** PENDING

### Milestone 19: Comprehensive Documentation & Deployment Prep
*   **Description:** Finalize all user and developer documentation. Prepare the application for a production-like deployment.
*   **Status:** PENDING

## Project Status Estimate (as of 2025-01-03)
*   **Completed Milestones:** 10 / 19 (Milestone 10 now complete with Google free tier detection)
*   **Percentage Complete by Milestones:** ~53%
*   **Percentage Complete by Scope of Work:** ~70-75%

### Scope-Based Completion Analysis:
*   **Core Infrastructure:** 95% Complete (all fundamental systems operational)
*   **API Management:** 100% Complete (full CRUD, encryption, role assignments)
*   **Routing Engine:** 100% Complete (advanced strategies, load balancing, failover)
*   **Logging & Analytics:** 90% Complete (comprehensive logging, basic analytics, missing only pricing data)
*   **Training System:** 100% Complete (full knowledge base integration and document processing)
*   **UI/UX:** 95% Complete (premium design system, all major pages implemented)
*   **Core API Functionality:** 100% Complete (multi-provider support, streaming, multimodal)

### Remaining Work Distribution:
*   **User Authentication & Management:** 12% of total scope
*   **Tool Integration & External Services:** 8% of total scope
*   **Advanced Enterprise Features:** 8% of total scope
*   **Documentation & Deployment:** 4% of total scope