// Enhanced orchestration utilities for multi-role AI team collaboration

export interface OrchestrationEvent {
  id: string;
  execution_id: string;
  type: OrchestrationEventType;
  timestamp: string;
  data: any;
  step_number?: number;
  role_id?: string;
  model_name?: string;
}

export type OrchestrationEventType = 
  | 'orchestration_started'
  | 'task_decomposed'
  | 'step_assigned'
  | 'step_started'
  | 'step_progress'
  | 'step_completed'
  | 'step_failed'
  | 'synthesis_started'
  | 'synthesis_progress'
  | 'orchestration_completed'
  | 'orchestration_failed'
  | 'moderator_commentary';

export interface TaskDependency {
  stepNumber: number;
  dependsOn: number[];
  canRunInParallel: boolean;
  priority: number;
}

export interface EnhancedOrchestrationStep {
  stepNumber: number;
  roleId: string;
  prompt: string;
  dependencies: number[];
  canRunInParallel: boolean;
  priority: number;
  estimatedDuration: number;
  outputFormat?: string;
  moderatorInstructions?: string;
}

export interface OrchestrationWorkflow {
  steps: EnhancedOrchestrationStep[];
  parallelGroups: number[][];
  totalEstimatedDuration: number;
  complexityScore: number;
}

// Create a Server-Sent Events stream for orchestration updates
export function createOrchestrationEventStream(executionId: string): ReadableStream {
  const encoder = new TextEncoder();
  
  return new ReadableStream({
    start(controller) {
      // Send initial connection event
      const initialEvent: OrchestrationEvent = {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'orchestration_started',
        timestamp: new Date().toISOString(),
        data: { message: 'Orchestration stream connected' }
      };
      
      const eventData = `data: ${JSON.stringify(initialEvent)}\n\n`;
      controller.enqueue(encoder.encode(eventData));
    },
    
    cancel() {
      console.log(`[Orchestration Stream] Stream cancelled for execution ${executionId}`);
    }
  });
}

// Enhanced task decomposition with dependency analysis
export async function decomposeTaskWithDependencies(
  originalPrompt: string,
  roles: Array<{ roleId: string; confidence: number; executionOrder: number }>,
  classificationApiKey: string
): Promise<OrchestrationWorkflow> {
  // Create system prompt for intelligent task decomposition
  const decompositionPrompt = `You are an expert AI orchestrator. Analyze this request and create an optimal workflow for multiple AI specialists.

Original Request: "${originalPrompt}"

Available Roles: ${roles.map(r => `${r.roleId} (confidence: ${r.confidence})`).join(', ')}

Create a detailed workflow with:
1. Task breakdown into specific steps
2. Dependencies between steps
3. Opportunities for parallel processing
4. Estimated complexity and duration
5. Clear handoff instructions between models

Respond in JSON format:
{
  "workflow": {
    "steps": [
      {
        "stepNumber": 1,
        "roleId": "role_name",
        "prompt": "specific task for this role",
        "dependencies": [],
        "canRunInParallel": false,
        "priority": 1,
        "estimatedDuration": 30000,
        "moderatorInstructions": "how to validate and hand off results"
      }
    ],
    "parallelGroups": [[1], [2, 3], [4]],
    "totalEstimatedDuration": 120000,
    "complexityScore": 7
  },
  "reasoning": "explanation of the workflow design"
}`;

  try {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-lite',
        messages: [
          { role: 'system', content: 'You are an expert AI workflow orchestrator.' },
          { role: 'user', content: decompositionPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1500,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      throw new Error(`Decomposition API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('Empty decomposition response');
    }

    const parsed = JSON.parse(content);
    return parsed.workflow;
    
  } catch (error) {
    console.warn(`[Task Decomposition] Error: ${error}, falling back to simple decomposition`);
    
    // Fallback to simple sequential decomposition
    return createFallbackWorkflow(originalPrompt, roles);
  }
}

// Fallback workflow creation for when AI decomposition fails
function createFallbackWorkflow(
  originalPrompt: string,
  roles: Array<{ roleId: string; confidence: number; executionOrder: number }>
): OrchestrationWorkflow {
  const sortedRoles = [...roles].sort((a, b) => a.executionOrder - b.executionOrder);
  
  const steps: EnhancedOrchestrationStep[] = sortedRoles.map((role, index) => ({
    stepNumber: index + 1,
    roleId: role.roleId,
    prompt: index === 0 
      ? `Handle the ${role.roleId} aspect of this request: "${originalPrompt}"`
      : `Continue with the ${role.roleId} aspect based on the previous step's output: {{previousOutput}}`,
    dependencies: index === 0 ? [] : [index],
    canRunInParallel: false,
    priority: index + 1,
    estimatedDuration: 45000, // 45 seconds default
    moderatorInstructions: `Validate the ${role.roleId} output and prepare for next step`
  }));

  return {
    steps,
    parallelGroups: steps.map((_, index) => [index + 1]),
    totalEstimatedDuration: steps.length * 45000,
    complexityScore: Math.min(steps.length * 2, 10)
  };
}

// Emit orchestration event to stream
export async function emitOrchestrationEvent(
  executionId: string,
  eventType: OrchestrationEventType,
  data: any,
  stepNumber?: number,
  roleId?: string,
  modelName?: string
): Promise<void> {
  const event: OrchestrationEvent = {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: eventType,
    timestamp: new Date().toISOString(),
    data,
    step_number: stepNumber,
    role_id: roleId,
    model_name: modelName
  };

  // Store event in database for persistence and replay
  try {
    // This would be implemented with your Supabase client
    console.log(`[Orchestration Event] ${eventType}:`, event);
    
    // In a real implementation, you'd store this in the orchestration_events table
    // and broadcast to connected clients via WebSocket or SSE
    
  } catch (error) {
    console.error(`[Orchestration Event] Failed to emit ${eventType}:`, error);
  }
}

// Generate moderator commentary for entertainment value
export function generateModeratorCommentary(
  eventType: OrchestrationEventType,
  stepData: any,
  roleId?: string
): string {
  const commentaries = {
    orchestration_started: [
      "🎬 Alright team, we've got an interesting challenge ahead!",
      "🚀 Let's break this down and see who's best suited for each part.",
      "🎯 Time to coordinate our AI specialists for optimal results."
    ],
    step_assigned: [
      `📋 Assigning the ${roleId} specialist to handle this part.`,
      `🎪 Our ${roleId} expert is stepping up to the plate!`,
      `⚡ Perfect match - ${roleId} is exactly what we need here.`
    ],
    step_started: [
      `🔥 ${roleId} is now working their magic...`,
      `⚙️ Watch ${roleId} tackle this challenge in real-time!`,
      `🎨 ${roleId} is crafting something special for us.`
    ],
    step_completed: [
      `✅ Excellent work from ${roleId}! Moving to the next phase.`,
      `🎉 ${roleId} delivered exactly what we needed. Handoff time!`,
      `💫 Beautiful execution by ${roleId}. The team is flowing perfectly.`
    ],
    synthesis_started: [
      "🧩 Now I'm weaving all these pieces together...",
      "🎭 Time for the grand finale - combining all our specialists' work!",
      "🌟 Watch as I synthesize these brilliant contributions into one cohesive result."
    ]
  };

  const options = commentaries[eventType] || ["🤖 Processing..."];
  return options[Math.floor(Math.random() * options.length)];
}

// Calculate optimal execution strategy (parallel vs sequential)
export function calculateExecutionStrategy(workflow: OrchestrationWorkflow): {
  strategy: 'sequential' | 'parallel' | 'hybrid';
  estimatedSpeedup: number;
  riskLevel: 'low' | 'medium' | 'high';
} {
  const totalSteps = workflow.steps.length;
  const parallelGroups = workflow.parallelGroups.length;
  
  if (parallelGroups === totalSteps) {
    return {
      strategy: 'sequential',
      estimatedSpeedup: 1.0,
      riskLevel: 'low'
    };
  }
  
  if (parallelGroups === 1) {
    return {
      strategy: 'parallel',
      estimatedSpeedup: totalSteps * 0.7, // Assume 70% efficiency in parallel
      riskLevel: 'high'
    };
  }
  
  return {
    strategy: 'hybrid',
    estimatedSpeedup: (totalSteps / parallelGroups) * 0.8, // 80% efficiency for hybrid
    riskLevel: 'medium'
  };
}
