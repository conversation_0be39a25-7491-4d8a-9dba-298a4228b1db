'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function HomePage() {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    // Immediate redirect to avoid any caching issues
    setIsRedirecting(true);

    // Use both replace and push as fallback
    const redirect = () => {
      try {
        router.replace('/dashboard');
      } catch (error) {
        // Fallback to window.location if router fails
        window.location.href = '/dashboard';
      }
    };

    // Immediate redirect
    redirect();

    // Fallback redirect after short delay
    const fallbackTimer = setTimeout(() => {
      if (window.location.pathname === '/') {
        window.location.href = '/dashboard';
      }
    }, 100);

    return () => clearTimeout(fallbackTimer);
  }, [router]);

  // Show a minimal loading state
  return (
    <div className="min-h-screen flex items-center justify-center bg-cream">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-2"></div>
        <p className="text-gray-600 text-sm">Loading...</p>
      </div>
    </div>
  );
}
