'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { type ChatConversation } from '@/types/chatHistory';

interface VirtualChatHistoryProps {
  conversations: ChatConversation[];
  currentConversation: ChatConversation | null;
  onLoadChat: (conversation: ChatConversation) => void;
  onDeleteChat: (id: string) => void;
  isLoading?: boolean;
  className?: string;
}

interface VirtualItem {
  index: number;
  conversation: ChatConversation;
  height: number;
  offset: number;
}

const ITEM_HEIGHT = 80; // Estimated height per chat item
const BUFFER_SIZE = 5; // Number of items to render outside viewport
const OVERSCAN = 3; // Additional items to render for smooth scrolling

// Memoized chat history item component
const ChatHistoryItem = React.memo(({
  conversation,
  currentConversation,
  onLoadChat,
  onDeleteChat,
  style
}: {
  conversation: ChatConversation;
  currentConversation: ChatConversation | null;
  onLoadChat: (conversation: ChatConversation) => void;
  onDeleteChat: (id: string) => void;
  style?: React.CSSProperties;
}) => {
  const isActive = currentConversation?.id === conversation.id;
  
  const handleClick = useCallback(() => {
    onLoadChat(conversation);
  }, [conversation, onLoadChat]);

  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this conversation?')) {
      onDeleteChat(conversation.id);
    }
  }, [conversation.id, onDeleteChat]);

  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  }, []);

  return (
    <div
      style={style}
      onClick={handleClick}
      className={`group flex items-center p-3 rounded-xl cursor-pointer transition-all duration-200 hover:bg-gray-50 ${
        isActive ? 'bg-orange-50 border border-orange-200' : 'border border-transparent'
      }`}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h4 className={`text-sm font-medium truncate ${
            isActive ? 'text-orange-700' : 'text-gray-900'
          }`}>
            {conversation.title}
          </h4>
          <span className={`text-xs ${
            isActive ? 'text-orange-500' : 'text-gray-500'
          }`}>
            {formatDate(conversation.updated_at)}
          </span>
        </div>
        
        {conversation.last_message_preview && (
          <p className="text-xs text-gray-600 truncate">
            {conversation.last_message_preview}
          </p>
        )}
        
        <div className="flex items-center justify-between mt-1">
          <span className="text-xs text-gray-500">
            {conversation.message_count || 0} messages
          </span>
          <button
            onClick={handleDelete}
            className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all duration-200 p-1"
            title="Delete conversation"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
});

ChatHistoryItem.displayName = 'ChatHistoryItem';

export const VirtualChatHistory: React.FC<VirtualChatHistoryProps> = ({
  conversations,
  currentConversation,
  onLoadChat,
  onDeleteChat,
  isLoading = false,
  className = ''
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // Calculate virtual items
  const virtualItems = useMemo((): VirtualItem[] => {
    return conversations.map((conversation, index) => ({
      index,
      conversation,
      height: ITEM_HEIGHT,
      offset: index * ITEM_HEIGHT
    }));
  }, [conversations]);

  const totalHeight = virtualItems.length * ITEM_HEIGHT;

  // Calculate visible range
  const visibleRange = useMemo(() => {
    if (containerHeight === 0) return { start: 0, end: 0 };

    const start = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE);
    const visibleCount = Math.ceil(containerHeight / ITEM_HEIGHT);
    const end = Math.min(
      virtualItems.length,
      start + visibleCount + BUFFER_SIZE + OVERSCAN
    );

    return { start, end };
  }, [scrollTop, containerHeight, virtualItems.length]);

  // Get visible items
  const visibleItems = useMemo(() => {
    return virtualItems.slice(visibleRange.start, visibleRange.end);
  }, [virtualItems, visibleRange]);

  // Handle scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    setScrollTop(scrollTop);
  }, []);

  // Update container height
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Scroll to active conversation
  useEffect(() => {
    if (currentConversation && scrollElementRef.current) {
      const activeIndex = conversations.findIndex(conv => conv.id === currentConversation.id);
      if (activeIndex !== -1) {
        const targetScrollTop = activeIndex * ITEM_HEIGHT;
        const maxScroll = totalHeight - containerHeight;
        const clampedScrollTop = Math.max(0, Math.min(targetScrollTop, maxScroll));
        
        scrollElementRef.current.scrollTop = clampedScrollTop;
      }
    }
  }, [currentConversation, conversations, totalHeight, containerHeight]);

  if (isLoading && conversations.length === 0) {
    return (
      <div className={`flex-1 overflow-hidden ${className}`}>
        <div className="p-4 space-y-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 h-4 w-3/4 rounded mb-2"></div>
              <div className="bg-gray-200 h-3 w-1/2 rounded mb-1"></div>
              <div className="bg-gray-200 h-3 w-1/4 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className={`flex-1 overflow-hidden ${className}`}>
        <div className="flex items-center justify-center h-full text-center py-8">
          <div>
            <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <p className="text-sm text-gray-500">No conversations yet</p>
            <p className="text-xs text-gray-400 mt-1">Start chatting to see your history</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`flex-1 overflow-hidden ${className}`}>
      <div
        ref={scrollElementRef}
        className="h-full overflow-y-auto"
        onScroll={handleScroll}
        style={{ scrollBehavior: 'smooth' }}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          {visibleItems.map((item) => (
            <div
              key={item.conversation.id}
              style={{
                position: 'absolute',
                top: item.offset,
                left: 0,
                right: 0,
                height: item.height,
                padding: '0 16px'
              }}
            >
              <ChatHistoryItem
                conversation={item.conversation}
                currentConversation={currentConversation}
                onLoadChat={onLoadChat}
                onDeleteChat={onDeleteChat}
              />
            </div>
          ))}
        </div>
      </div>
      
      {/* Loading indicator for additional data */}
      {isLoading && conversations.length > 0 && (
        <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-2 text-center">
          <div className="text-xs text-gray-500">Loading more conversations...</div>
        </div>
      )}
    </div>
  );
};

export default VirtualChatHistory;
