-- R<PERSON><PERSON><PERSON> App: Data Loss Investigation and Recovery Script
-- Run this in your Supabase SQL Editor to investigate and potentially recover lost data

-- =====================================================
-- STEP 1: INVESTIGATE CURRENT STATE
-- =====================================================

-- Check all training jobs for your configuration
SELECT 
    id,
    name,
    description,
    status,
    created_at,
    updated_at,
    (training_data->>'file_count')::int as file_count_in_data,
    training_data->'knowledge_base' is not null as has_knowledge_base
FROM training_jobs 
WHERE custom_api_config_id = 'YOUR_CONFIG_ID_HERE'  -- Replace with your actual config ID
ORDER BY created_at DESC;

-- Check training files for each job
SELECT 
    tf.id,
    tf.training_job_id,
    tf.original_filename,
    tf.file_size,
    tf.processing_status,
    tf.created_at,
    tj.name as job_name,
    tj.created_at as job_created_at
FROM training_files tf
JOIN training_jobs tj ON tf.training_job_id = tj.id
WHERE tj.custom_api_config_id = 'YOUR_CONFIG_ID_HERE'  -- Replace with your actual config ID
ORDER BY tj.created_at DESC, tf.created_at DESC;

-- Count files per training job
SELECT 
    tj.id as job_id,
    tj.name as job_name,
    tj.created_at as job_created,
    COUNT(tf.id) as actual_file_count,
    (tj.training_data->>'file_count')::int as reported_file_count
FROM training_jobs tj
LEFT JOIN training_files tf ON tj.id = tf.training_job_id
WHERE tj.custom_api_config_id = 'YOUR_CONFIG_ID_HERE'  -- Replace with your actual config ID
GROUP BY tj.id, tj.name, tj.created_at, tj.training_data
ORDER BY tj.created_at DESC;

-- =====================================================
-- STEP 2: CHECK FOR ORPHANED FILES (if any exist)
-- =====================================================

-- Look for any training files that might be orphaned
SELECT 
    tf.id,
    tf.original_filename,
    tf.training_job_id,
    tf.created_at,
    'ORPHANED - Job deleted' as status
FROM training_files tf
LEFT JOIN training_jobs tj ON tf.training_job_id = tj.id
WHERE tj.id IS NULL;

-- =====================================================
-- STEP 3: RECOVERY OPTIONS (if data exists elsewhere)
-- =====================================================

-- If you have backups or can identify the lost files, you can:
-- 1. Re-upload the files through the UI
-- 2. Or manually insert file records if you have the data

-- Example recovery insert (DO NOT RUN without proper data):
/*
INSERT INTO training_files (
    training_job_id,
    original_filename,
    file_size,
    file_type,
    storage_path,
    processing_status,
    extracted_content,
    metadata
) VALUES (
    'LATEST_JOB_ID_HERE',
    'recovered_file.txt',
    1024,
    'text/plain',
    'path/to/file',
    'completed',
    'File content here...',
    '{"word_count": 100, "character_count": 1024}'::jsonb
);
*/

-- =====================================================
-- STEP 4: PREVENT FUTURE DATA LOSS
-- =====================================================

-- Check current foreign key constraints
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'training_files';

-- =====================================================
-- STEP 5: IMMEDIATE SAFEGUARD (OPTIONAL)
-- =====================================================

-- If you want to temporarily disable CASCADE DELETE to prevent further data loss:
-- WARNING: Only do this if you understand the implications

/*
-- Drop the existing foreign key constraint
ALTER TABLE training_files 
DROP CONSTRAINT IF EXISTS training_files_training_job_id_fkey;

-- Add it back with RESTRICT instead of CASCADE
ALTER TABLE training_files 
ADD CONSTRAINT training_files_training_job_id_fkey 
FOREIGN KEY (training_job_id) 
REFERENCES training_jobs(id) 
ON DELETE RESTRICT;
*/

-- =====================================================
-- STEP 6: VERIFICATION QUERIES
-- =====================================================

-- After running recovery, verify the state:
SELECT 
    'Total training jobs' as metric,
    COUNT(*) as count
FROM training_jobs 
WHERE custom_api_config_id = 'YOUR_CONFIG_ID_HERE'

UNION ALL

SELECT 
    'Total training files' as metric,
    COUNT(*) as count
FROM training_files tf
JOIN training_jobs tj ON tf.training_job_id = tj.id
WHERE tj.custom_api_config_id = 'YOUR_CONFIG_ID_HERE'

UNION ALL

SELECT 
    'Latest job file count' as metric,
    COUNT(*) as count
FROM training_files tf
JOIN training_jobs tj ON tf.training_job_id = tj.id
WHERE tj.custom_api_config_id = 'YOUR_CONFIG_ID_HERE'
    AND tj.id = (
        SELECT id FROM training_jobs 
        WHERE custom_api_config_id = 'YOUR_CONFIG_ID_HERE' 
        ORDER BY created_at DESC 
        LIMIT 1
    );

-- =====================================================
-- INSTRUCTIONS:
-- =====================================================
-- 1. Replace 'YOUR_CONFIG_ID_HERE' with your actual custom_api_config_id
-- 2. Run the investigation queries first to understand what happened
-- 3. If files are truly lost, you'll need to re-upload them
-- 4. The code fix in training/page.tsx will prevent this from happening again
-- 5. Consider the safeguard option if you want extra protection
