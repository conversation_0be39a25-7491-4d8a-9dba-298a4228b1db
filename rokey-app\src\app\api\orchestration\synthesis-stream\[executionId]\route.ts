// Direct streaming endpoint for orchestration synthesis phase
// This endpoint mimics the single-role streaming behavior for the synthesis phase

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/utils/supabase/server';
import { broadcastOrchestrationEvent } from '@/app/api/orchestration/stream/[executionId]/route';
import { EnhancedModerator } from '@/utils/moderatorUtils';
import crypto from 'crypto';

// Dynamic chunk storage function
async function storeDynamicChunk(synthesisId: string, conversationId: string, chunks: string[], chunkIndex: number): Promise<void> {
  const supabase = createSupabaseServerClientOnRequest();

  const { error } = await supabase
    .from('synthesis_storage')
    .upsert({
      synthesis_id: synthesisId,
      conversation_id: conversationId,
      complete_synthesis: chunks.join(''), // Reconstruct complete synthesis
      chunks: chunks,
      total_chunks: chunks.length,
      created_at: new Date().toISOString(),
      last_access_time: new Date().toISOString()
    });

  if (error) {
    console.error(`[Synthesis Stream] Database error storing dynamic chunk ${chunkIndex}:`, error);
    throw new Error(`Failed to store dynamic chunk: ${error.message}`);
  }

  console.log(`[Synthesis Stream] Stored dynamic chunk ${chunkIndex} in database (total chunks: ${chunks.length})`);
}

// Import the streaming utilities dynamically to ensure they're available
let createFirstTokenTrackingStream: any;
try {
  const streamingUtils = require('@/utils/streamingUtils');
  createFirstTokenTrackingStream = streamingUtils.createFirstTokenTrackingStream;
  console.log('[Synthesis Stream] Successfully imported createFirstTokenTrackingStream');
} catch (importError) {
  console.error('[Synthesis Stream] Error importing streamingUtils:', importError);
  // Fallback implementation if the import fails
  createFirstTokenTrackingStream = (stream: ReadableStream) => stream;
}

// Robust fetch implementation with retries and error handling
async function robustFetch(
  url: string,
  options: RequestInit & { timeout?: number } = {},
  retries = 2,
  backoff = 300
): Promise<Response> {
  const timeout = options.timeout || 60000; // Default 60s timeout
  
  try {
    // Create an AbortController to handle timeouts
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    // Add the signal to the options
    const fetchOptions = {
      ...options,
      signal: controller.signal
    };
    
    // Attempt the fetch
    const response = await fetch(url, fetchOptions);
    clearTimeout(timeoutId);
    
    return response;
  } catch (error: any) {
    // Handle retries for network errors or timeouts
    if (retries > 0) {
      console.warn(`Fetch attempt failed, retrying (${retries} attempts left): ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, backoff));
      return robustFetch(url, options, retries - 1, backoff * 2);
    }
    
    // If we're out of retries, throw a more informative error
    throw new Error(`Fetch failed after multiple attempts: ${error.message}`);
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { executionId: string } }
) {
  const { executionId } = params;
  
  console.log(`[Synthesis Stream] Starting direct stream for execution ${executionId}`);
  
  if (!executionId) {
    return NextResponse.json(
      { error: 'Execution ID is required' },
      { status: 400 }
    );
  }

  const supabase = createSupabaseServerClientOnRequest();
  console.log(`[Synthesis Stream] Supabase client created`);
  
  try {
    // Verify execution exists and is in the synthesis phase
    console.log(`[Synthesis Stream] Fetching execution data for ${executionId}`);
    const { data: execution, error } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (error) {
      console.error(`[Synthesis Stream] Error fetching execution: ${error.message}`);
      return NextResponse.json(
        { error: `Orchestration execution not found: ${error.message}` },
        { status: 404 }
      );
    }
    
    if (!execution) {
      console.error(`[Synthesis Stream] Execution not found for ID: ${executionId}`);
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }
    
    console.log(`[Synthesis Stream] Found execution with status: ${execution.status}`);

    // Get all completed steps for synthesis
    console.log(`[Synthesis Stream] Fetching completed steps for ${executionId}`);
    const { data: allSteps, error: stepsError } = await supabase
      .from('orchestration_steps')
      .select('step_number, role_id, response, prompt')
      .eq('execution_id', executionId)
      .eq('status', 'completed')
      .order('step_number', { ascending: true });

    if (stepsError) {
      console.error(`[Synthesis Stream] Error fetching steps: ${stepsError.message}`);
      return NextResponse.json(
        { error: `Error fetching steps: ${stepsError.message}` },
        { status: 500 }
      );
    }

    if (!allSteps || allSteps.length === 0) {
      console.error(`[Synthesis Stream] No completed steps found for execution: ${executionId}`);
      return NextResponse.json(
        { error: 'No completed steps found for synthesis' },
        { status: 400 }
      );
    }
    
    console.log(`[Synthesis Stream] Found ${allSteps.length} completed steps for synthesis`);

    // Extract the original prompt from the first step
    const originalPrompt = allSteps[0]?.prompt?.split('"')[1] || 'user request';

    // Create synthesis prompt
    const synthesisPrompt = `You are the final moderator synthesizing the work of multiple AI specialists who collaborated on this request: "${originalPrompt}"

Here are the outputs from each specialist:

${allSteps.map(s => `**${s.role_id.toUpperCase()} (Step ${s.step_number}):**
${s.response}

`).join('\n')}

Your task is to:
1. Combine these outputs into a single, cohesive, and complete response
2. Ensure the final result fully addresses the original user request
3. Present it in a clear, well-structured format
4. Include any necessary explanations or instructions for the user

Provide the final, polished response that the user will receive:`;

    // Use the classification API key for synthesis
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      return NextResponse.json(
        { error: 'Classification API key not found' },
        { status: 500 }
      );
    }
    
    // Create a moderator instance for generating commentary
    const moderator = new EnhancedModerator(classificationApiKey, executionId);

    // Call synthesis API with streaming - DIRECT PROVIDER CALL
    // This mimics the single-role flow by making a direct call to the provider
    console.log(`[Synthesis Stream] Making API call to Gemini for synthesis`);
    
    let synthesisResponse;
    try {
      synthesisResponse = await robustFetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${classificationApiKey}`,
          'User-Agent': 'RoKey/1.0 (Synthesis)',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify({
          model: 'gemini-2.0-flash-001',
          messages: [{ role: 'user', content: synthesisPrompt }],
          stream: true, // Enable streaming for synthesis
          temperature: 0.3,
          max_tokens: 8000 // Restore original limit for complete synthesis
        })
      });

      console.log(`[Synthesis Stream] API response status: ${synthesisResponse.status}`);
      
      if (!synthesisResponse.ok) {
        const errorText = await synthesisResponse.text().catch(() => 'Could not read error response');
        console.error(`[Synthesis Stream] API error: ${synthesisResponse.status}, ${errorText}`);
        return NextResponse.json(
          { error: `Synthesis API call failed: ${synthesisResponse.status}, ${errorText}` },
          { status: synthesisResponse.status }
        );
      }
      
      console.log(`[Synthesis Stream] API call successful, setting up stream transformer`);
    } catch (apiError) {
      console.error(`[Synthesis Stream] API call exception: ${apiError}`);
      return NextResponse.json(
        { error: `Synthesis API call exception: ${apiError}` },
        { status: 500 }
      );
    }

    // Create a real-time streaming transformer with dynamic chunking
    console.log(`[Synthesis Stream] Creating real-time dynamic streaming transformer`);
    const reader = synthesisResponse.body!.getReader();
    const encoder = new TextEncoder();
    const decoder = new TextDecoder();
    let fullResponse = '';
    let accumulatedContent = '';

    // Dynamic chunking state
    let currentChunkIndex = 0;
    let currentChunkContent = '';
    let storedChunks: string[] = [];
    const conversationId = `synthesis_${executionId}`;
    const synthesisId = `synthesis_${conversationId}_${Date.now()}`;

    // Chunking configuration
    const CHUNK_LIMITS = {
      MAX_CHARS: 20000,
      MIN_CHUNK_SIZE: 1000,
      SEMANTIC_BREAK_BONUS: 500
    };

    // Dynamic chunking helper function
    const createDynamicChunk = async () => {
      if (currentChunkContent.length < CHUNK_LIMITS.MIN_CHUNK_SIZE) {
        return; // Don't create tiny chunks
      }

      // Find a good semantic break point
      let chunkToStore = currentChunkContent;
      const semanticBreaks = ['\n\n', '\n', '. ', '! ', '? '];

      for (const breakPoint of semanticBreaks) {
        const lastBreak = currentChunkContent.lastIndexOf(breakPoint);
        if (lastBreak > CHUNK_LIMITS.MIN_CHUNK_SIZE) {
          chunkToStore = currentChunkContent.substring(0, lastBreak + breakPoint.length);
          currentChunkContent = currentChunkContent.substring(lastBreak + breakPoint.length);
          break;
        }
      }

      // If no good break found, use the whole content
      if (chunkToStore === currentChunkContent) {
        currentChunkContent = '';
      }

      storedChunks.push(chunkToStore);
      console.log(`[Synthesis Stream] Created dynamic chunk ${currentChunkIndex}: ${chunkToStore.length} chars`);

      // Store chunk in database immediately
      try {
        await storeDynamicChunk(synthesisId, conversationId, storedChunks, currentChunkIndex);
        currentChunkIndex++;
      } catch (error) {
        console.error(`[Synthesis Stream] Error storing dynamic chunk: ${error}`);
      }
    };

    const transformedStream = new ReadableStream({
      async start(controller) {
        console.log(`[Synthesis Stream] Real-time dynamic streaming transformer started`);
        try {
          let chunkCount = 0;
          while (true) {
            console.log(`[Synthesis Stream] Reading chunk ${chunkCount++}`);
            const { done, value } = await reader.read();
            
            if (done) {
              console.log(`[Synthesis Stream] Stream complete, received ${fullResponse.length} chars`);

              // Create final chunk if there's remaining content
              if (currentChunkContent.length > 0) {
                storedChunks.push(currentChunkContent);
                console.log(`[Synthesis Stream] Created final chunk ${currentChunkIndex}: ${currentChunkContent.length} chars`);

                // Store final chunk in database
                try {
                  await storeDynamicChunk(synthesisId, conversationId, storedChunks, currentChunkIndex);
                } catch (error) {
                  console.error(`[Synthesis Stream] Error storing final chunk: ${error}`);
                }
              }

              // Stream is complete, update the database with the full response
              try {
                // Log the current status before updating
                const { data: currentExecution } = await supabase
                  .from('orchestration_executions')
                  .select('status')
                  .eq('id', executionId)
                  .single();

                console.log(`[Synthesis Stream] Current execution status: ${currentExecution?.status}`);
                console.log(`[Synthesis Stream] Final synthesis stored in ${storedChunks.length} chunks`);

                // Update with a valid status
                await supabase
                  .from('orchestration_executions')
                  .update({
                    status: 'completed',
                    completed_at: new Date().toISOString(),
                    final_response: fullResponse
                  })
                  .eq('id', executionId);
                
                console.log(`[Synthesis Stream] Updated database with final response (${fullResponse.length} chars)`);
                
                // Broadcast completion event to the orchestration stream
                const { data: execution } = await supabase
                  .from('orchestration_executions')
                  .select('created_at, status')
                  .eq('id', executionId)
                  .single();
                
                const totalDuration = execution?.created_at ? Date.now() - new Date(execution.created_at).getTime() : 0;
                
                // Get the total number of steps
                const { data: steps } = await supabase
                  .from('orchestration_steps')
                  .select('step_number')
                  .eq('execution_id', executionId);
                
                const totalSteps = steps?.length || 0;
                
                // Broadcast completion event
                broadcastOrchestrationEvent(executionId, {
                  id: crypto.randomUUID(),
                  execution_id: executionId,
                  type: 'orchestration_completed',
                  timestamp: new Date().toISOString(),
                  data: {
                    commentary: moderator.generateLiveCommentary('orchestration_completed', { totalSteps }),
                    finalResult: fullResponse,
                    totalSteps: totalSteps,
                    totalDuration: totalDuration
                  }
                });
                
                // Send a final 'done' event and [DONE] message
                controller.enqueue(encoder.encode("event: done\ndata: {}\n\n"));
                controller.enqueue(encoder.encode("data: [DONE]\n\n"));
              } catch (dbError) {
                console.error(`[Synthesis Stream] Database update error: ${dbError}`);
              }
              
              controller.close();
              break;
            }
            
            const chunk = decoder.decode(value, { stream: true });
            console.log(`[Synthesis Stream] Received chunk: ${chunk.length} chars`);
            
            // Extract content from the chunk to build the full response
            const lines = chunk.split('\n');
            console.log(`[Synthesis Stream] Processing ${lines.length} lines`);
            
            for (const line of lines) {
              if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                try {
                  const jsonData = line.substring(6);
                  const parsed = JSON.parse(jsonData);
                  if (parsed.choices?.[0]?.delta?.content) {
                    const content = parsed.choices[0].delta.content;
                    fullResponse += content;
                    accumulatedContent += content;
                    currentChunkContent += content;

                    console.log(`[Synthesis Stream] Added content: ${content.length} chars, total: ${accumulatedContent.length}, current chunk: ${currentChunkContent.length}`);

                    // Broadcast real-time synthesis streaming with accumulated content
                    broadcastOrchestrationEvent(executionId, {
                      id: crypto.randomUUID(),
                      execution_id: executionId,
                      type: 'synthesis_streaming',
                      timestamp: new Date().toISOString(),
                      data: {
                        commentary: "🎨 Streaming synthesis response...",
                        partialResult: accumulatedContent,
                        progress: Math.min(0.9, accumulatedContent.length / 1000),
                        chunkIndex: currentChunkIndex,
                        chunkProgress: currentChunkContent.length
                      }
                    });

                    // Check if we need to create a new chunk dynamically
                    if (currentChunkContent.length >= CHUNK_LIMITS.MAX_CHARS - CHUNK_LIMITS.SEMANTIC_BREAK_BONUS) {
                      try {
                        await createDynamicChunk();
                      } catch (chunkError) {
                        console.error(`[Synthesis Stream] Error creating dynamic chunk: ${chunkError}`);
                        // Continue streaming even if chunking fails
                      }
                    }
                  }
                } catch (e) {
                  console.log(`[Synthesis Stream] JSON parse error for line: ${line.substring(0, 50)}...`);
                  // Ignore JSON parse errors for individual chunks
                }
              }
            }

            // Forward the chunk unchanged
            controller.enqueue(value);
            console.log(`[Synthesis Stream] Forwarded chunk to client`);
          }
        } catch (error) {
          console.error(`[Synthesis Stream] Error: ${error}`);
          controller.error(error);
        }
      }
    });

    // Add first token tracking to the stream
    console.log(`[Synthesis Stream] Adding first token tracking to stream`);
    let trackedStream;
    try {
      trackedStream = createFirstTokenTrackingStream(
        transformedStream,
        'Gemini',
        'gemini-2.0-flash-001'
      );
      console.log(`[Synthesis Stream] First token tracking added successfully`);
    } catch (trackingError) {
      console.error(`[Synthesis Stream] Error adding first token tracking: ${trackingError}`);
      // Fall back to the original stream if tracking fails
      trackedStream = transformedStream;
    }

    // Create a custom header to identify this as a synthesis stream
    const headers = new Headers({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'X-RoKey-Stream-Type': 'synthesis',
      'X-RoKey-Execution-ID': executionId
    });

    // Return the stream directly to the client
    // This is the key change - we're bypassing the orchestration event system
    // and directly streaming the provider's response to the client
    console.log(`[Synthesis Stream] Returning stream response to client`);
    
    // Set up a fallback in case the stream fails
    // After 30 seconds, if the stream hasn't completed, mark the execution as completed
    setTimeout(async () => {
      try {
        const { data: execution } = await supabase
          .from('orchestration_executions')
          .select('status')
          .eq('id', executionId)
          .single();
        
        if (execution?.status !== 'completed') {
          console.log(`[Synthesis Stream] Fallback timeout triggered - execution still not completed after 30s`);
          
          // Update the execution status
          await supabase
            .from('orchestration_executions')
            .update({
              status: 'completed',
              completed_at: new Date().toISOString(),
              final_response: 'Synthesis timed out. Please check the individual specialist outputs.'
            })
            .eq('id', executionId);
          
          // Broadcast completion event
          broadcastOrchestrationEvent(executionId, {
            id: crypto.randomUUID(),
            execution_id: executionId,
            type: 'orchestration_completed',
            timestamp: new Date().toISOString(),
            data: {
              commentary: '⚠️ Synthesis timed out, but specialist outputs are available.',
              finalResult: 'Synthesis timed out. Please check the individual specialist outputs.',
              totalSteps: 0,
              totalDuration: 30000
            }
          });
        }
      } catch (fallbackError) {
        console.error(`[Synthesis Stream] Fallback error: ${fallbackError}`);
      }
    }, 30000);
    
    return new Response(trackedStream, { headers });

  } catch (error) {
    console.error(`[Synthesis Stream] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}