import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers'; // Correctly import cookies as a value

// Type for the cookie store returned by next/headers' cookies()
// This ensures we match the exact type provided by Next.js in these contexts.
type NextCookieStore = ReturnType<typeof cookies>;

// This is the standard setup for creating a Supabase server client
// in Next.js App Router (Server Components, Route Handlers, Server Actions).
export function createSupabaseServerClientOnRequest() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookies().get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookies().set({ name, value, ...options });
          } catch (error) {
            // This error can be ignored if running in a Server Component
            // where cookies can't be set directly. Cookie setting should be
            // handled in Server Actions or Route Handlers.
            // console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            // To remove a cookie using the `set` method from `next/headers`,
            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.
            // However, @supabase/ssr expects a remove method that might be different.
            // The common pattern for @supabase/ssr's remove is to call set with an empty value and the options.
            cookies().set({ name, value: '', ...options });
          } catch (error) {
            // Similar to set, this might fail in a Server Component.
            // console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);
          }
        },
      },
    }
  );
}
