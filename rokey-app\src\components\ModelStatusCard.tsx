'use client';

import React, { useState } from 'react';
import { 
  ClockI<PERSON>,
  CpuChipIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface ModelStatusCardProps {
  roleId: string;
  modelName: string;
  status: 'waiting' | 'assigned' | 'working' | 'completed' | 'failed';
  progress: number;
  output?: string;
  duration?: number;
  quality?: number;
}

export const ModelStatusCard: React.FC<ModelStatusCardProps> = ({
  roleId,
  modelName,
  status,
  progress,
  output,
  duration,
  quality
}) => {
  const [showOutput, setShowOutput] = useState(false);

  const getStatusConfig = () => {
    switch (status) {
      case 'waiting':
        return {
          icon: ClockIcon,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          label: 'Waiting',
          description: 'Waiting for assignment'
        };
      case 'assigned':
        return {
          icon: CpuChipIcon,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          label: 'Assigned',
          description: 'Ready to start'
        };
      case 'working':
        return {
          icon: CpuChipIcon,
          color: 'text-orange-500',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          label: 'Working',
          description: 'Processing request'
        };
      case 'completed':
        return {
          icon: CheckCircleIcon,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          label: 'Completed',
          description: 'Task finished'
        };
      case 'failed':
        return {
          icon: ExclamationTriangleIcon,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          label: 'Failed',
          description: 'Error occurred'
        };
      default:
        return {
          icon: ClockIcon,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          label: 'Unknown',
          description: 'Status unknown'
        };
    }
  };

  const statusConfig = getStatusConfig();
  const StatusIcon = statusConfig.icon;

  const formatRoleName = (roleId: string) => {
    return roleId
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return null;
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getQualityColor = (quality?: number) => {
    if (!quality) return 'text-gray-500';
    if (quality >= 0.8) return 'text-green-500';
    if (quality >= 0.6) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getQualityLabel = (quality?: number) => {
    if (!quality) return 'N/A';
    if (quality >= 0.9) return 'Excellent';
    if (quality >= 0.8) return 'Good';
    if (quality >= 0.6) return 'Fair';
    return 'Poor';
  };

  return (
    <div className={`relative p-4 rounded-lg border-2 transition-all duration-300 ${statusConfig.bgColor} ${statusConfig.borderColor}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-5 h-5 ${statusConfig.color} ${status === 'working' ? 'animate-pulse' : ''}`} />
          <div>
            <h4 className="text-sm font-semibold text-gray-900">
              {formatRoleName(roleId)}
            </h4>
            <p className="text-xs text-gray-600">{modelName}</p>
          </div>
        </div>
        
        <div className="text-right">
          <span className={`text-xs font-medium ${statusConfig.color}`}>
            {statusConfig.label}
          </span>
          {duration && (
            <p className="text-xs text-gray-500 mt-1">
              {formatDuration(duration)}
            </p>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {(status === 'working' || status === 'completed') && (
        <div className="mb-3">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-gray-600">Progress</span>
            <span className="text-xs text-gray-600">{Math.round(progress * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${
                status === 'completed' ? 'bg-green-500' : 'bg-orange-500'
              }`}
              style={{ width: `${progress * 100}%` }}
            />
          </div>
        </div>
      )}

      {/* Quality Score */}
      {quality !== undefined && status === 'completed' && (
        <div className="mb-3 flex items-center justify-between">
          <span className="text-xs text-gray-600">Quality</span>
          <div className="flex items-center space-x-1">
            <SparklesIcon className={`w-3 h-3 ${getQualityColor(quality)}`} />
            <span className={`text-xs font-medium ${getQualityColor(quality)}`}>
              {getQualityLabel(quality)} ({Math.round(quality * 100)}%)
            </span>
          </div>
        </div>
      )}

      {/* Output Preview */}
      {output && status === 'completed' && (
        <div className="mt-3">
          <button
            onClick={() => setShowOutput(!showOutput)}
            className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
          >
            {showOutput ? (
              <EyeSlashIcon className="w-3 h-3" />
            ) : (
              <EyeIcon className="w-3 h-3" />
            )}
            <span>{showOutput ? 'Hide' : 'View'} Output</span>
          </button>
          
          {showOutput && (
            <div className="mt-2 p-2 bg-white rounded border text-xs text-gray-700 max-h-32 overflow-y-auto">
              <pre className="whitespace-pre-wrap font-mono">
                {output.length > 200 ? `${output.substring(0, 200)}...` : output}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Working Animation */}
      {status === 'working' && (
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
      )}

      {/* Completion Celebration */}
      {status === 'completed' && (
        <div className="absolute top-2 right-2">
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
            <CheckCircleIcon className="w-4 h-4 text-white" />
          </div>
        </div>
      )}
    </div>
  );
};
