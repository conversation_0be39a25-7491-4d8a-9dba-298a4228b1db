-- Smart Indexing and Intelligent Retrieval System Cleanup
-- This script removes all smart indexing related tables, columns, and constraints
-- while preserving the basic knowledge base functionality

-- =====================================================
-- STEP 1: Drop Smart Indexing Tables
-- =====================================================

-- Drop knowledge retrieval logs table
DROP TABLE IF EXISTS knowledge_retrieval_logs CASCADE;

-- Drop knowledge retrieval config table  
DROP TABLE IF EXISTS knowledge_retrieval_config CASCADE;

-- Drop document chunks table (semantic search)
DROP TABLE IF EXISTS document_chunks CASCADE;

-- =====================================================
-- STEP 2: Remove Smart Indexing Columns from training_files
-- =====================================================

-- Remove intelligent retrieval columns from training_files table
DO $$
BEGIN
    -- Remove document summary column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'document_summary') THEN
        ALTER TABLE training_files DROP COLUMN document_summary;
        RAISE NOTICE 'Dropped column: document_summary';
    END IF;

    -- Remove topic tags column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'topic_tags') THEN
        ALTER TABLE training_files DROP COLUMN topic_tags;
        RAISE NOTICE 'Dropped column: topic_tags';
    END IF;

    -- Remove content type column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'content_type') THEN
        ALTER TABLE training_files DROP COLUMN content_type;
        RAISE NOTICE 'Dropped column: content_type';
    END IF;

    -- Remove key entities column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'key_entities') THEN
        ALTER TABLE training_files DROP COLUMN key_entities;
        RAISE NOTICE 'Dropped column: key_entities';
    END IF;

    -- Remove relevance keywords column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'relevance_keywords') THEN
        ALTER TABLE training_files DROP COLUMN relevance_keywords;
        RAISE NOTICE 'Dropped column: relevance_keywords';
    END IF;

    -- Remove token count column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'token_count') THEN
        ALTER TABLE training_files DROP COLUMN token_count;
        RAISE NOTICE 'Dropped column: token_count';
    END IF;

    -- Remove last relevance score column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'last_relevance_score') THEN
        ALTER TABLE training_files DROP COLUMN last_relevance_score;
        RAISE NOTICE 'Dropped column: last_relevance_score';
    END IF;

    -- Remove last query hash column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'training_files' AND column_name = 'last_query_hash') THEN
        ALTER TABLE training_files DROP COLUMN last_query_hash;
        RAISE NOTICE 'Dropped column: last_query_hash';
    END IF;
END $$;

-- =====================================================
-- STEP 3: Drop Related Indexes
-- =====================================================

-- Drop indexes related to smart indexing (if they exist)
DROP INDEX IF EXISTS idx_training_files_content_type;
DROP INDEX IF EXISTS idx_training_files_token_count;
DROP INDEX IF EXISTS idx_training_files_last_relevance_score;
DROP INDEX IF EXISTS idx_training_files_topic_tags;
DROP INDEX IF EXISTS idx_training_files_relevance_keywords;
DROP INDEX IF EXISTS idx_document_chunks_file_id;
DROP INDEX IF EXISTS idx_document_chunks_token_count;
DROP INDEX IF EXISTS idx_knowledge_retrieval_config_custom_api_config_id;
DROP INDEX IF EXISTS idx_knowledge_retrieval_logs_custom_api_config_id;
DROP INDEX IF EXISTS idx_knowledge_retrieval_logs_created_at;

-- =====================================================
-- STEP 4: Verify Cleanup
-- =====================================================

-- Verify that basic knowledge base tables remain intact
DO $$
BEGIN
    -- Check that training_jobs table still exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'training_jobs') THEN
        RAISE NOTICE 'SUCCESS: training_jobs table preserved';
    ELSE
        RAISE WARNING 'WARNING: training_jobs table missing!';
    END IF;

    -- Check that training_files table still exists with basic columns
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'training_files') THEN
        RAISE NOTICE 'SUCCESS: training_files table preserved';
        
        -- Verify essential columns remain
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'training_files' AND column_name = 'extracted_content') THEN
            RAISE NOTICE 'SUCCESS: extracted_content column preserved';
        ELSE
            RAISE WARNING 'WARNING: extracted_content column missing!';
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'training_files' AND column_name = 'original_filename') THEN
            RAISE NOTICE 'SUCCESS: original_filename column preserved';
        ELSE
            RAISE WARNING 'WARNING: original_filename column missing!';
        END IF;
    ELSE
        RAISE WARNING 'WARNING: training_files table missing!';
    END IF;

    -- Verify smart indexing tables are gone
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'knowledge_retrieval_config') THEN
        RAISE NOTICE 'SUCCESS: knowledge_retrieval_config table removed';
    ELSE
        RAISE WARNING 'WARNING: knowledge_retrieval_config table still exists!';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'knowledge_retrieval_logs') THEN
        RAISE NOTICE 'SUCCESS: knowledge_retrieval_logs table removed';
    ELSE
        RAISE WARNING 'WARNING: knowledge_retrieval_logs table still exists!';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'document_chunks') THEN
        RAISE NOTICE 'SUCCESS: document_chunks table removed';
    ELSE
        RAISE WARNING 'WARNING: document_chunks table still exists!';
    END IF;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Smart Indexing Cleanup Complete!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Removed:';
    RAISE NOTICE '- knowledge_retrieval_config table';
    RAISE NOTICE '- knowledge_retrieval_logs table';
    RAISE NOTICE '- document_chunks table';
    RAISE NOTICE '- 8 smart indexing columns from training_files';
    RAISE NOTICE '- Related indexes and constraints';
    RAISE NOTICE '';
    RAISE NOTICE 'Preserved:';
    RAISE NOTICE '- training_jobs table (complete)';
    RAISE NOTICE '- training_files table (basic functionality)';
    RAISE NOTICE '- All existing training data and files';
    RAISE NOTICE '=================================================';
END $$;
