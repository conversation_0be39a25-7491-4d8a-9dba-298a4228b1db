-- Migration for user_custom_roles table

CREATE TABLE public.user_custom_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  custom_api_config_id UUID NOT NULL REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
  role_id TEXT NOT NULL CHECK (char_length(role_id) <= 30),
  name TEXT NOT NULL CHECK (char_length(name) <= 100),
  description TEXT CHECK (char_length(description) <= 500),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

  CONSTRAINT unique_custom_role_id_per_config UNIQUE (custom_api_config_id, role_id)
);

-- Optional: Trigger to update 'updated_at' timestamp
CREATE TRIGGER handle_updated_at_user_custom_roles
BEFORE UPDATE ON public.user_custom_roles
FOR EACH ROW
EXECUTE FUNCTION moddatetime (updated_at);

-- RLS Policies (Placeholder - to be defined with actual auth integration)
-- Ensure that this table is initially locked down if <PERSON><PERSON> is enabled globally.
-- For now, assuming RLS will be more comprehensively handled in Milestone 10.
-- ALTER TABLE public.user_custom_roles ENABLE ROW LEVEL SECURITY;

-- Example policies (to be refined when user auth is in place):
-- CREATE POLICY "Users can manage their own custom roles" 
-- ON public.user_custom_roles
-- FOR ALL
-- USING ( auth.uid() = (SELECT user_id FROM public.custom_api_configs WHERE id = custom_api_config_id) )
-- WITH CHECK ( auth.uid() = (SELECT user_id FROM public.custom_api_configs WHERE id = custom_api_config_id) );

COMMENT ON TABLE public.user_custom_roles IS 'Stores user-defined custom roles associated with a specific Custom API Configuration.';
COMMENT ON COLUMN public.user_custom_roles.role_id IS 'Short, user-defined ID for the role (e.g., \"my_blog_writer\"), max 30 chars, unique per config.';
COMMENT ON COLUMN public.user_custom_roles.name IS 'User-friendly name for the custom role (e.g., \"My Blog Post Writer\").'; 