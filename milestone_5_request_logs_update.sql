-- <PERSON><PERSON>t for Milestone 5: Enhance request_logs table

-- Add new columns
ALTER TABLE public.request_logs
ADD COLUMN IF NOT EXISTS error_source TEXT,
ADD COLUMN IF NOT EXISTS error_details_zod TEXT,
ADD COLUMN IF NOT EXISTS llm_provider_name TEXT,
ADD COLUMN IF NOT EXISTS llm_model_name TEXT, -- Stores the predefined_model_id used for the request
ADD COLUMN IF NOT EXISTS llm_provider_status_code INTEGER,
ADD COLUMN IF NOT EXISTS llm_provider_latency_ms INTEGER,
ADD COLUMN IF NOT EXISTS processing_duration_ms INTEGER,
ADD COLUMN IF NOT EXISTS cost NUMERIC, -- Placeholder for M10, initially NULL
ADD COLUMN IF NOT EXISTS tokens_prompt INTEGER,
ADD COLUMN IF NOT EXISTS tokens_completion INTEGER,
ADD COLUMN IF NOT EXISTS is_multimodal BOOLEAN DEFAULT FALSE;

-- Rename existing columns for clarity
ALTER TABLE public.request_logs
RENAME COLUMN request_payload TO request_payload_summary;

ALTER TABLE public.request_logs
RENAME COLUMN response_payload TO response_payload_summary;

COMMENT ON COLUMN public.request_logs.request_payload_summary IS 'Summary of the request payload (JSONB: messages_count, model_requested_passthrough, streaming, temperature, max_tokens). Avoids storing full sensitive PII.';
COMMENT ON COLUMN public.request_logs.response_payload_summary IS 'Summary of the response payload (JSONB: usage from LLM, finish_reason, has_error indication). Avoids storing full sensitive PII.';
COMMENT ON COLUMN public.request_logs.error_source IS 'Source of the error (e.g., "RoKey", "LLM Provider").';
COMMENT ON COLUMN public.request_logs.error_details_zod IS 'Stringified Zod validation error details, if applicable.';
COMMENT ON COLUMN public.request_logs.llm_provider_name IS 'Name of the downstream LLM provider used (e.g., "OpenAI", "Google").';
COMMENT ON COLUMN public.request_logs.llm_model_name IS 'The specific model ID (predefined_model_id) used for the LLM request.';
COMMENT ON COLUMN public.request_logs.llm_provider_status_code IS 'HTTP status code received from the LLM provider.';
COMMENT ON COLUMN public.request_logs.llm_provider_latency_ms IS 'Latency of the request to the LLM provider in milliseconds.';
COMMENT ON COLUMN public.request_logs.processing_duration_ms IS 'Total processing duration of the request within RoKey in milliseconds (Response Timestamp - Request Timestamp - LLM Provider Latency).';
COMMENT ON COLUMN public.request_logs.cost IS 'Estimated cost of the API call (placeholder for Milestone 10).';
COMMENT ON COLUMN public.request_logs.tokens_prompt IS 'Number of prompt tokens, if available from LLM response.';
COMMENT ON COLUMN public.request_logs.tokens_completion IS 'Number of completion tokens, if available from LLM response.';
COMMENT ON COLUMN public.request_logs.is_multimodal IS 'Flag indicating if the request contained multimodal content (e.g., images).';

SELECT 'Milestone 5 request_logs update script executed successfully.'; 