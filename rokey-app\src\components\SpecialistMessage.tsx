'use client';

import React, { useState } from 'react';
import { ChatroomMessage } from '@/utils/orchestrationUtils';
import { getSpecialistPersonality } from '@/config/specialistPersonalities';
import { 
  ClockIcon,
  EyeIcon,
  EyeSlashIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface SpecialistMessageProps {
  message: ChatroomMessage;
}

export const SpecialistMessage: React.FC<SpecialistMessageProps> = ({ message }) => {
  const [showFullContent, setShowFullContent] = useState(false);
  const personality = getSpecialistPersonality(message.sender);
  
  if (!personality) {
    return null;
  }

  const getColorClasses = (color: string) => {
    const colorMap: { [key: string]: { bg: string; border: string; text: string; avatar: string } } = {
      purple: {
        bg: 'bg-purple-50',
        border: 'border-purple-200',
        text: 'text-purple-700',
        avatar: 'bg-purple-500'
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-700',
        avatar: 'bg-green-500'
      },
      orange: {
        bg: 'bg-orange-50',
        border: 'border-orange-200',
        text: 'text-orange-700',
        avatar: 'bg-orange-500'
      },
      indigo: {
        bg: 'bg-indigo-50',
        border: 'border-indigo-200',
        text: 'text-indigo-700',
        avatar: 'bg-indigo-500'
      },
      teal: {
        bg: 'bg-teal-50',
        border: 'border-teal-200',
        text: 'text-teal-700',
        avatar: 'bg-teal-500'
      },
      gray: {
        bg: 'bg-gray-50',
        border: 'border-gray-200',
        text: 'text-gray-700',
        avatar: 'bg-gray-500'
      }
    };
    
    return colorMap[color] || colorMap.gray;
  };

  const colors = getColorClasses(personality.color);
  
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageIcon = () => {
    switch (message.messageType) {
      case 'acknowledgment':
        return <SparklesIcon className="w-4 h-4" />;
      case 'work_update':
        return <ClockIcon className="w-4 h-4 animate-pulse" />;
      case 'final_output':
        return <DocumentTextIcon className="w-4 h-4" />;
      case 'question':
        return <span className="text-lg">❓</span>;
      default:
        return <span className="text-lg">{personality.avatar}</span>;
    }
  };

  const shouldTruncate = message.content.length > 300;
  const displayContent = shouldTruncate && !showFullContent 
    ? message.content.substring(0, 300) + '...'
    : message.content;

  const hasCodeBlocks = message.content.includes('```');
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  
  const renderContent = () => {
    if (!hasCodeBlocks) {
      return (
        <div className="whitespace-pre-wrap text-gray-800">
          {displayContent}
        </div>
      );
    }

    // Split content by code blocks
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(message.content)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: message.content.substring(lastIndex, match.index)
        });
      }
      
      // Add code block
      parts.push({
        type: 'code',
        language: match[1] || 'text',
        content: match[2]
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add remaining text
    if (lastIndex < message.content.length) {
      parts.push({
        type: 'text',
        content: message.content.substring(lastIndex)
      });
    }

    return (
      <div className="space-y-3">
        {parts.map((part, index) => (
          part.type === 'code' ? (
            <div key={index} className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-400 uppercase tracking-wide">
                  {part.language}
                </span>
                <CodeBracketIcon className="w-4 h-4 text-gray-400" />
              </div>
              <pre className="text-sm text-gray-100 overflow-x-auto">
                <code>{part.content}</code>
              </pre>
            </div>
          ) : (
            <div key={index} className="whitespace-pre-wrap text-gray-800">
              {shouldTruncate && !showFullContent && index === parts.length - 1
                ? part.content.substring(0, Math.max(0, 300 - parts.slice(0, index).join('').length)) + '...'
                : part.content
              }
            </div>
          )
        ))}
      </div>
    );
  };

  return (
    <div className="flex space-x-3 animate-fade-in">
      {/* Avatar */}
      <div className={`flex-shrink-0 w-10 h-10 rounded-full ${colors.avatar} flex items-center justify-center text-white font-semibold shadow-md`}>
        <span className="text-lg">{personality.avatar}</span>
      </div>
      
      {/* Message Content */}
      <div className="flex-1 max-w-4xl">
        {/* Header */}
        <div className="flex items-center space-x-2 mb-1">
          <span className={`font-semibold ${colors.text}`}>
            {personality.name}
          </span>
          <div className="flex items-center space-x-1 text-gray-500">
            {getMessageIcon()}
            <span className="text-xs">
              {formatTime(message.timestamp)}
            </span>
          </div>
          {message.metadata?.stepNumber && (
            <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
              Step {message.metadata.stepNumber}
            </span>
          )}
        </div>
        
        {/* Message Bubble */}
        <div className={`${colors.bg} ${colors.border} border rounded-lg p-4 shadow-sm`}>
          {renderContent()}
          
          {/* Progress Bar for work updates */}
          {message.messageType === 'work_update' && message.metadata?.progress && (
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${colors.avatar.replace('bg-', 'bg-')}`}
                  style={{ width: `${message.metadata.progress * 100}%` }}
                />
              </div>
              <span className="text-xs text-gray-600 mt-1 block">
                {Math.round(message.metadata.progress * 100)}% complete
              </span>
            </div>
          )}
          
          {/* Show More/Less Button */}
          {shouldTruncate && (
            <button
              onClick={() => setShowFullContent(!showFullContent)}
              className="mt-3 flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
            >
              {showFullContent ? (
                <>
                  <EyeSlashIcon className="w-4 h-4" />
                  <span>Show less</span>
                </>
              ) : (
                <>
                  <EyeIcon className="w-4 h-4" />
                  <span>Show more</span>
                </>
              )}
            </button>
          )}
        </div>
        
        {/* Reactions (placeholder for future enhancement) */}
        {message.reactions && message.reactions.length > 0 && (
          <div className="flex items-center space-x-2 mt-2">
            {message.reactions.map((reaction, index) => (
              <span 
                key={index}
                className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-sm"
              >
                {reaction.emoji}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
