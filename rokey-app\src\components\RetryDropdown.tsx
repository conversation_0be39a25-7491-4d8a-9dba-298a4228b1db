'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ArrowPathIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { type DisplayApiKey } from '@/types/apiKeys';

// Simple cache for API keys by config ID
const keysCache = new Map<string, { keys: DisplayApiKey[]; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

interface RetryDropdownProps {
  configId: string;
  onRetry: (apiKeyId?: string) => void;
  className?: string;
  disabled?: boolean;
}

export default function RetryDropdown({
  configId,
  onRetry,
  className = '',
  disabled = false
}: RetryDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [availableKeys, setAvailableKeys] = useState<DisplayApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Optimized fetch with caching
  const fetchAvailableKeys = useCallback(async (useCache = true) => {
    if (!configId) return;

    // Check cache first
    if (useCache) {
      const cached = keysCache.get(configId);
      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
        setAvailableKeys(cached.keys);
        setHasInitialLoad(true);
        return;
      }
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/keys?custom_config_id=${configId}`);
      if (response.ok) {
        const keys: DisplayApiKey[] = await response.json();
        const activeKeys = keys.filter(key => key.status === 'active');

        // Update cache
        keysCache.set(configId, {
          keys: activeKeys,
          timestamp: Date.now()
        });

        setAvailableKeys(activeKeys);
        setHasInitialLoad(true);
      }
    } catch (error) {
      console.error('Failed to fetch available keys:', error);
    } finally {
      setIsLoading(false);
    }
  }, [configId]);

  // Prefetch keys on component mount for instant dropdown opening
  useEffect(() => {
    if (configId && !hasInitialLoad) {
      fetchAvailableKeys(true);
    }
  }, [configId, fetchAvailableKeys, hasInitialLoad]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleRetryClick = (apiKeyId?: string) => {
    setIsOpen(false);
    onRetry(apiKeyId);
  };

  const handleRefreshKeys = (e: React.MouseEvent) => {
    e.stopPropagation();
    fetchAvailableKeys(false); // Force refresh, bypass cache
  };

  const handleDropdownToggle = () => {
    if (!isOpen && availableKeys.length === 0 && !hasInitialLoad) {
      // If we don't have keys yet, fetch them
      fetchAvailableKeys(true);
    }
    setIsOpen(!isOpen);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Retry Button */}
      <button
        onClick={handleDropdownToggle}
        disabled={disabled}
        className={`
          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer
          ${disabled
            ? 'text-gray-300 cursor-not-allowed'
            : 'text-gray-500 hover:text-gray-700 hover:bg-white/20'
          }
        `}
        title="Retry with different model"
      >
        <ArrowPathIcon className={`w-4 h-4 stroke-2 ${isLoading ? 'animate-spin' : ''}`} />
        <ChevronDownIcon className="w-3 h-3 stroke-2" />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left">
          <div className="py-1">
            {/* Header with refresh button */}
            <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
              <span className="text-xs font-medium text-gray-600">Retry Options</span>
              <button
                onClick={handleRefreshKeys}
                disabled={isLoading}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                title="Refresh available models"
              >
                <ArrowPathIcon className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
            </div>

            {/* Retry with same model option */}
            <button
              onClick={() => handleRetryClick()}
              className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
            >
              <ArrowPathIcon className="w-4 h-4 text-gray-500" />
              <span>Retry with same model</span>
            </button>

            {/* Separator */}
            {(availableKeys.length > 0 || isLoading) && (
              <div className="border-t border-gray-100 my-1"></div>
            )}

            {/* Loading state */}
            {isLoading && (
              <div className="px-3 py-2 text-sm text-gray-500 flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                <span>Loading models...</span>
              </div>
            )}

            {/* Available API keys */}
            {availableKeys.map((key) => (
              <button
                key={key.id}
                onClick={() => handleRetryClick(key.id)}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50"
                disabled={isLoading}
              >
                <div className="flex items-center justify-between w-full">
                  <span className="font-medium">{key.label}</span>
                  <span className="text-xs text-gray-500 capitalize">{key.provider}</span>
                </div>
                <div className="text-xs text-gray-500 mt-0.5">
                  Temperature: {key.temperature}
                </div>
              </button>
            ))}

            {/* No keys available */}
            {!isLoading && availableKeys.length === 0 && hasInitialLoad && (
              <div className="px-3 py-2 text-sm text-gray-500">
                No alternative models available
              </div>
            )}

            {/* Cache info */}
            {availableKeys.length > 0 && !isLoading && (
              <div className="px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between">
                <span>{availableKeys.length} model{availableKeys.length !== 1 ? 's' : ''} available</span>
                {(() => {
                  const cached = keysCache.get(configId);
                  const isCached = cached && (Date.now() - cached.timestamp) < CACHE_TTL;
                  return isCached ? (
                    <span className="text-green-500 text-xs">●</span>
                  ) : null;
                })()}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
