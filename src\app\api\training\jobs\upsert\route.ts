import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import type { NewTrainingJob } from '@/types/training';

// POST /api/training/jobs/upsert
// Safely creates or updates a training job to prevent duplicates and data loss
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientOnRequest();

  try {
    const jobData = await request.json() as NewTrainingJob;
    const { custom_api_config_id, name, description, training_data, parameters } = jobData;

    if (!custom_api_config_id || !name) {
      return NextResponse.json({ 
        error: 'Missing required fields: custom_api_config_id, name' 
      }, { status: 400 });
    }

    console.log(`[Training Job Upsert] Processing upsert for config: ${custom_api_config_id}`);

    // Use the database function to safely upsert the training job
    const { data, error } = await supabase.rpc('upsert_training_job', {
      p_custom_api_config_id: custom_api_config_id,
      p_name: name,
      p_description: description,
      p_training_data: training_data,
      p_parameters: parameters
    });

    if (error) {
      console.error('[Training Job Upsert] Database error:', error);
      return NextResponse.json({ 
        error: 'Failed to upsert training job', 
        details: error.message 
      }, { status: 500 });
    }

    if (!data || data.length === 0) {
      console.error('[Training Job Upsert] No data returned from upsert function');
      return NextResponse.json({ 
        error: 'Upsert operation failed - no data returned' 
      }, { status: 500 });
    }

    const result = data[0];
    const isNewJob = result.is_new_job;

    console.log(`[Training Job Upsert] ${isNewJob ? 'Created new' : 'Updated existing'} training job:`, result.id);

    // Invalidate training cache for immediate effect
    if (!isNewJob) {
      try {
        const { trainingDataCache } = await import('@/lib/cache/trainingCache');
        const invalidated = trainingDataCache.invalidate(custom_api_config_id);
        console.log(`[Training Job Upsert] Cache invalidated for config: ${custom_api_config_id} (${invalidated ? 'success' : 'not cached'})`);
      } catch (error) {
        console.warn(`[Training Job Upsert] Cache invalidation error:`, error);
      }
    }

    // Return appropriate status code and response
    return NextResponse.json({
      id: result.id,
      custom_api_config_id: result.custom_api_config_id,
      name: result.name,
      description: result.description,
      status: result.status,
      training_data: result.training_data,
      parameters: result.parameters,
      created_at: result.created_at,
      updated_at: result.updated_at,
      operation: isNewJob ? 'created' : 'updated',
      message: isNewJob 
        ? 'New training job created successfully'
        : 'Existing training job updated successfully - all files preserved'
    }, { 
      status: isNewJob ? 201 : 200 
    });

  } catch (e: any) {
    console.error('[Training Job Upsert] Error:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ 
        error: 'Invalid request body: Malformed JSON.' 
      }, { status: 400 });
    }
    return NextResponse.json({ 
      error: 'An unexpected error occurred', 
      details: e.message 
    }, { status: 500 });
  }
}
